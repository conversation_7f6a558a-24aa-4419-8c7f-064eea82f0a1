#!/usr/bin/env python3
"""
Debug Email Monitoring

This script will help us debug what's happening with the email monitoring.
"""

import os
import sys
import traceback
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add app to path
sys.path.append('.')

def debug_email_monitoring():
    """Debug the email monitoring process step by step."""
    print("🔍 Debugging Email Monitoring Process")
    print("=" * 60)
    
    try:
        from app.models import get_db
        from app.services.email_monitor import EmailMonitor, EmailProcessor
        from app.services.ai_summarizer import get_ai_summarizer
        from app.services.whatsapp_notifier import get_whatsapp_notifier
        from app.services.email_replier import get_email_replier
        
        # Get configuration
        config = {
            "imap_host": os.getenv("IMAP_HOST"),
            "imap_port": int(os.getenv("IMAP_PORT", "993")),
            "imap_username": os.getenv("IMAP_USERNAME"),
            "imap_password": os.getenv("IMAP_PASSWORD"),
            "use_ssl": os.getenv("IMAP_USE_SSL", "True").lower() == "true",
            "mailbox": os.getenv("IMAP_MAILBOX", "INBOX"),
            "allowed_sender_email": os.getenv("ALLOWED_SENDER_EMAIL"),
            
            "use_openai": os.getenv("USE_OPENAI", "True").lower() == "true",
            "openai_api_key": os.getenv("OPENAI_API_KEY"),
            "openai_model": os.getenv("OPENAI_MODEL", "gpt-4"),
            
            "mock_whatsapp": os.getenv("MOCK_WHATSAPP", "False").lower() == "true",
            "meta_api_token": os.getenv("META_API_TOKEN"),
            "meta_phone_number_id": os.getenv("META_PHONE_NUMBER_ID"),
            "team_numbers": os.getenv("TEAM_NUMBERS", "").split(","),
            
            "smtp_host": os.getenv("SMTP_HOST"),
            "smtp_port": int(os.getenv("SMTP_PORT", "587")),
            "smtp_username": os.getenv("SMTP_USERNAME"),
            "smtp_password": os.getenv("SMTP_PASSWORD"),
            "smtp_use_ssl": os.getenv("SMTP_USE_SSL", "False").lower() == "true",
            "default_sender": os.getenv("DEFAULT_SENDER"),
            "mock_email": os.getenv("MOCK_EMAIL", "False").lower() == "true",
        }
        
        print("📧 Configuration:")
        print(f"   IMAP: {config['imap_username']} @ {config['imap_host']}")
        print(f"   Allowed sender: {config['allowed_sender_email']}")
        print(f"   WhatsApp: {len(config['team_numbers'])} recipients")
        
        # Step 1: Test email connection
        print("\n🔌 Step 1: Testing email connection...")
        monitor = EmailMonitor(
            host=config['imap_host'],
            username=config['imap_username'],
            password=config['imap_password'],
            port=config['imap_port'],
            use_ssl=config['use_ssl'],
            mailbox=config['mailbox']
        )
        
        if not monitor.connect():
            print("❌ Failed to connect to email server")
            return False
        
        print("✅ Connected to email server")
        
        if not monitor.select_mailbox():
            print("❌ Failed to select mailbox")
            monitor.disconnect()
            return False
        
        print("✅ Mailbox selected")
        
        # Step 2: Search for emails
        print("\n🔍 Step 2: Searching for emails...")
        all_emails = monitor.search_emails('ALL')
        print(f"   📊 Total emails: {len(all_emails)}")
        
        unread_emails = monitor.search_emails('UNSEEN')
        print(f"   📊 Unread emails: {len(unread_emails)}")
        
        # Get recent emails (last 10)
        recent_emails = all_emails[-10:] if len(all_emails) >= 10 else all_emails
        print(f"   📊 Checking last {len(recent_emails)} emails...")
        
        # Step 3: Check database
        print("\n🗄️ Step 3: Checking database...")
        db = get_db()
        from app.models import EmailLog
        
        existing_count = db.query(EmailLog).count()
        print(f"   📊 Existing emails in database: {existing_count}")
        
        # Step 4: Process emails
        print("\n📧 Step 4: Processing emails...")
        processor = EmailProcessor(db, config['allowed_sender_email'])
        
        new_emails_found = 0
        processed_emails = 0
        
        for i, email_id in enumerate(recent_emails, 1):
            print(f"\n   📧 Email {i}/{len(recent_emails)} (ID: {email_id})")
            
            # Fetch email
            email_data = monitor.fetch_email(email_id)
            if not email_data:
                print(f"      ❌ Failed to fetch email")
                continue
            
            print(f"      📨 From: {email_data['sender']['email']}")
            print(f"      📝 Subject: {email_data['subject'][:50]}...")
            print(f"      📅 Date: {email_data['date']}")
            
            # Check if already processed
            existing = db.query(EmailLog).filter_by(message_id=email_data['message_id']).first()
            if existing:
                print(f"      ♻️ Already processed (DB ID: {existing.id})")
                continue
            
            new_emails_found += 1
            print(f"      🆕 New email found!")
            
            # Check sender filtering
            if config['allowed_sender_email'] and email_data['sender']['email'] != config['allowed_sender_email']:
                print(f"      🔍 Filtered out (not from allowed sender)")
                continue
            
            print(f"      ✅ From allowed sender - processing...")
            
            # Process email
            email_log = processor.process_email(email_data)
            if email_log:
                processed_emails += 1
                print(f"      ✅ Stored in database (ID: {email_log.id})")
                
                # Step 5: AI Processing
                print(f"      🤖 AI processing...")
                try:
                    ai_summarizer = get_ai_summarizer(config)
                    email_content = email_data['body']['text'] or email_data['body']['html'] or "No content"
                    ai_result = ai_summarizer.process_email(email_log, email_content, db)
                    
                    if ai_result.get('success'):
                        print(f"      ✅ AI processing completed")
                        print(f"         📝 Summary: {email_log.summary[:50] if email_log.summary else 'None'}...")
                        print(f"         📱 WhatsApp: {email_log.whatsapp_summary[:50] if email_log.whatsapp_summary else 'None'}...")
                    else:
                        print(f"      ❌ AI processing failed")
                except Exception as e:
                    print(f"      ❌ AI processing error: {str(e)}")
                
                # Step 6: WhatsApp notification
                print(f"      📱 Sending WhatsApp notification...")
                try:
                    whatsapp_notifier = get_whatsapp_notifier(config)
                    notifications = whatsapp_notifier.send_notification(email_log, db)
                    
                    if notifications:
                        print(f"      ✅ WhatsApp sent to {len(notifications)} recipients")
                        for notif in notifications:
                            print(f"         📱 {notif.recipient}: {notif.status}")
                    else:
                        print(f"      ❌ No WhatsApp notifications sent")
                except Exception as e:
                    print(f"      ❌ WhatsApp error: {str(e)}")
                
                # Step 7: Auto-reply
                print(f"      📤 Sending auto-reply...")
                try:
                    email_replier = get_email_replier(config)
                    reply = email_replier.send_reply(email_log, db)
                    
                    if reply and reply.status == 'sent':
                        print(f"      ✅ Auto-reply sent to {reply.reply_to}")
                    else:
                        print(f"      ❌ Auto-reply failed")
                except Exception as e:
                    print(f"      ❌ Auto-reply error: {str(e)}")
            else:
                print(f"      ❌ Failed to process email")
        
        # Summary
        print(f"\n📊 Processing Summary:")
        print(f"   📧 Total emails checked: {len(recent_emails)}")
        print(f"   🆕 New emails found: {new_emails_found}")
        print(f"   ✅ Emails processed: {processed_emails}")
        
        db.close()
        monitor.disconnect()
        
        return processed_emails > 0
        
    except Exception as e:
        print(f"❌ Debug failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run debug monitoring."""
    success = debug_email_monitoring()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Email monitoring is working!")
    else:
        print("⚠️ Issues found with email monitoring")
        print("\n💡 Common issues:")
        print("- Check if you sent the email from the correct address")
        print("- Verify the email arrived in your inbox")
        print("- Check if the email was already processed")

if __name__ == "__main__":
    main()
