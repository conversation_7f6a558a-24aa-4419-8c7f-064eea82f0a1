#!/usr/bin/env python3
"""
Debug why voice calls are not being initiated in the notification flow
"""

import sys
import os
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables
load_dotenv()

def debug_notification_service_init():
    """Debug notification service initialization"""
    print("🔍 Debugging Notification Service Initialization")
    print("=" * 60)
    
    try:
        from app.services.notification_service import get_notification_service
        from start_monitoring_improved import get_config
        
        config = get_config()
        
        print("📋 Configuration:")
        print(f"  • Voice Calling Enabled: {config.get('voice_calling_enabled', False)}")
        print(f"  • Voice Provider: {config.get('voice_provider', 'N/A')}")
        print(f"  • Twilio Account SID: {config.get('twilio_account_sid', 'N/A')[:10]}..." if config.get('twilio_account_sid') else "  • Twilio Account SID: Not set")
        print(f"  • Twilio Auth Token: {config.get('twilio_auth_token', 'N/A')[:10]}..." if config.get('twilio_auth_token') else "  • Twilio Auth Token: Not set")
        print(f"  • Twilio From Number: {config.get('twilio_from_number', 'N/A')}")
        
        # Initialize notification service
        notification_service = get_notification_service(config)
        
        print(f"\n🔔 Notification Service:")
        print(f"  • Service initialized: ✅")
        print(f"  • WhatsApp notifier: {'✅' if notification_service.whatsapp_notifier else '❌'}")
        print(f"  • SMS service: {'✅' if notification_service.sms_service else '❌'}")
        print(f"  • Twilio caller: {'✅' if notification_service.twilio_caller else '❌'}")
        
        if notification_service.twilio_caller:
            print(f"  • Caller type: {type(notification_service.twilio_caller).__name__}")
            if hasattr(notification_service.twilio_caller, 'account_sid'):
                print(f"  • Caller account: {notification_service.twilio_caller.account_sid[:10]}...")
        else:
            print(f"  • ❌ Twilio caller is None - voice calls will be skipped!")
        
        return notification_service, config
        
    except Exception as e:
        print(f"❌ Error initializing notification service: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def test_voice_calling_condition(notification_service):
    """Test the voice calling condition"""
    print(f"\n🔍 Testing Voice Calling Condition")
    print("=" * 50)
    
    if not notification_service:
        print(f"❌ No notification service available")
        return False
    
    # Check the condition used in send_notifications
    has_twilio_caller = bool(notification_service.twilio_caller)
    
    print(f"📞 Voice calling condition check:")
    print(f"  • self.twilio_caller exists: {has_twilio_caller}")
    print(f"  • self.twilio_caller value: {notification_service.twilio_caller}")
    
    if has_twilio_caller:
        print(f"  ✅ Voice calls should be initiated")
        return True
    else:
        print(f"  ❌ Voice calls will be skipped")
        return False

def test_complete_notification_flow():
    """Test the complete notification flow"""
    print(f"\n🧪 Testing Complete Notification Flow")
    print("=" * 60)
    
    try:
        from app.services.notification_service import get_notification_service
        from app.models import EmailLog, get_db
        from start_monitoring_improved import get_config
        from datetime import datetime
        
        config = get_config()
        notification_service = get_notification_service(config)
        
        if not notification_service:
            print(f"❌ Cannot get notification service")
            return False
        
        # Create test email
        db = get_db()
        test_email = EmailLog(
            message_id=f"voice_flow_test_{int(datetime.now().timestamp())}",
            sender="Vishnu Bala Guru M <<EMAIL>>",
            recipient="<EMAIL>",
            subject="Voice Call Flow Test",
            status="processed",
            whatsapp_summary="This is a test to check if voice calls are initiated in the notification flow. All employees should receive voice calls."
        )
        
        db.add(test_email)
        db.commit()
        
        print(f"📧 Created test email: {test_email.id}")
        print(f"📞 Twilio caller available: {'✅' if notification_service.twilio_caller else '❌'}")
        
        # Test the notification flow
        print(f"\n📤 Sending notifications...")
        results = notification_service.send_notifications(test_email, db)
        
        print(f"\n📊 Notification Results:")
        print(f"  • WhatsApp notifications: {len(results.get('whatsapp_notifications', []))}")
        print(f"  • SMS notifications: {len(results.get('sms_notifications', []))}")
        print(f"  • Voice calls: {len(results.get('voice_calls', []))}")
        print(f"  • Success count: {results.get('success_count', 0)}")
        print(f"  • Failure count: {results.get('failure_count', 0)}")
        
        # Show voice call details
        voice_calls = results.get('voice_calls', [])
        if voice_calls:
            print(f"\n📞 Voice Call Details:")
            for i, call in enumerate(voice_calls, 1):
                print(f"  {i}. {call.get('employee_name')} ({call.get('recipient')})")
                print(f"     Status: {call.get('status')}")
                print(f"     Call ID: {call.get('vapi_call_id', 'None')}")
                if call.get('error_message'):
                    print(f"     Error: {call.get('error_message')}")
        else:
            print(f"\n❌ No voice calls were initiated!")
            print(f"   This means the voice calling condition failed")
        
        db.close()
        return len(voice_calls) > 0
        
    except Exception as e:
        print(f"❌ Error in notification flow test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_voice_calling_logs():
    """Check recent logs for voice calling"""
    print(f"\n📋 Checking Recent Voice Call Logs")
    print("=" * 50)
    
    try:
        from app.models import VoiceCall, get_db
        
        db = get_db()
        
        # Get recent voice calls
        recent_calls = db.query(VoiceCall).order_by(VoiceCall.id.desc()).limit(10).all()
        
        if recent_calls:
            print(f"📞 Found {len(recent_calls)} recent voice calls:")
            for call in recent_calls:
                print(f"  • {call.employee_name} ({call.recipient})")
                print(f"    Status: {call.status}")
                print(f"    Call ID: {call.vapi_call_id}")
                print(f"    Created: {call.created_at}")
                if call.error_message:
                    print(f"    Error: {call.error_message}")
                print()
        else:
            print(f"❌ No voice call records found in database")
            print(f"   This confirms voice calls are not being initiated")
        
        db.close()
        return len(recent_calls) > 0
        
    except Exception as e:
        print(f"❌ Error checking voice call logs: {str(e)}")
        return False

def main():
    """Main debug function"""
    print("🚀 Voice Calling Notification Flow Debug")
    print("=" * 70)
    
    # Step 1: Debug notification service initialization
    notification_service, config = debug_notification_service_init()
    
    # Step 2: Test voice calling condition
    voice_condition_ok = test_voice_calling_condition(notification_service)
    
    # Step 3: Check existing voice call logs
    has_voice_logs = check_voice_calling_logs()
    
    # Step 4: Test complete notification flow
    if notification_service:
        flow_test_ok = test_complete_notification_flow()
    else:
        flow_test_ok = False
    
    print(f"\n🎯 Debug Summary")
    print("=" * 50)
    print(f"✅ Notification Service: {'OK' if notification_service else 'Failed'}")
    print(f"✅ Voice Calling Condition: {'OK' if voice_condition_ok else 'Failed'}")
    print(f"✅ Voice Call Logs: {'Found' if has_voice_logs else 'None'}")
    print(f"✅ Flow Test: {'OK' if flow_test_ok else 'Failed'}")
    
    if not voice_condition_ok:
        print(f"\n❌ ROOT CAUSE: Twilio caller is not initialized properly")
        print(f"💡 Check:")
        print(f"   1. VOICE_CALLING_ENABLED=True in .env")
        print(f"   2. All Twilio credentials are set")
        print(f"   3. Voice calling configuration in notification service")
    
    if flow_test_ok:
        print(f"\n🎉 Voice calls should be working now!")
        print(f"📱 Check if employees received voice calls")

if __name__ == "__main__":
    main()
