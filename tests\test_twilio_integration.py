#!/usr/bin/env python3
"""
Test Twilio Voice and SMS integration
"""

import sys
import os
import time
from dotenv import load_dotenv
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables from .env file
load_dotenv()

def test_twilio_config():
    """Test Twilio configuration"""
    print("🔧 Testing Twilio Configuration...")
    
    required_vars = [
        'TWILIO_ACCOUNT_SID',
        'TWILIO_AUTH_TOKEN',
        'TWILIO_SMS_FROM_NUMBER',
        'TWILIO_FROM_NUMBER'
    ]
    
    config_ok = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {value[:8]}..." if len(value) > 8 else f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: Not set")
            config_ok = False
    
    return config_ok

def test_twilio_caller():
    """Test Twilio caller initialization"""
    print("\n📞 Testing Twilio Caller...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.twilio_caller import get_twilio_caller
        
        # Test configuration
        config = {
            'voice_calling_enabled': True,
            'twilio_account_sid': os.getenv('TWILIO_ACCOUNT_SID', 'test_sid'),
            'twilio_auth_token': os.getenv('TWILIO_AUTH_TOKEN', 'test_token'),
            'twilio_from_number': os.getenv('TWILIO_FROM_NUMBER', '+**********')
        }
        
        caller = get_twilio_caller(config)
        
        if caller:
            print("   ✅ Twilio caller initialized successfully")
            print(f"   📊 Account SID: {caller.account_sid[:8]}...")
            print(f"   📊 From Number: {caller.from_number}")
            return True
        else:
            print("   ❌ Failed to initialize Twilio caller")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False

def test_twilio_sms():
    """Test Twilio SMS service initialization"""
    print("\n📱 Testing Twilio SMS Service...")
    
    try:
        from app.services.twilio_sms import get_sms_service
        
        # Test configuration
        config = {
            'sms_enabled': True,
            'twilio_account_sid': os.getenv('TWILIO_ACCOUNT_SID', 'test_sid'),
            'twilio_auth_token': os.getenv('TWILIO_AUTH_TOKEN', 'test_token'),
            'twilio_sms_from_number': os.getenv('TWILIO_SMS_FROM_NUMBER', 'EmailAgent')
        }
        
        sms_service = get_sms_service(config)
        
        if sms_service:
            print("   ✅ Twilio SMS service initialized successfully")
            print(f"   📊 Account SID: {sms_service.account_sid[:8]}...")
            print(f"   📊 From Number: {sms_service.from_number}")
            return True
        else:
            print("   ❌ Failed to initialize Twilio SMS service")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False

def test_notification_service():
    """Test notification service with Twilio"""
    print("\n🔔 Testing Notification Service with Twilio...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.notification_service import NotificationService
        
        # Test configuration
        config = {
            'voice_calling_enabled': True,
            'twilio_account_sid': os.getenv('TWILIO_ACCOUNT_SID', 'test_sid'),
            'twilio_auth_token': os.getenv('TWILIO_AUTH_TOKEN', 'test_token'),
            'twilio_from_number': os.getenv('TWILIO_FROM_NUMBER', '+**********'),
            'sms_enabled': True,
            'twilio_sms_from_number': os.getenv('TWILIO_SMS_FROM_NUMBER', 'EmailAgent'),
            # WhatsApp config (mock)
            'meta_api_token': 'test_token',
            'meta_phone_number_id': 'test_phone_id',
            'team_numbers': ['+**********']
        }
        
        notification_service = NotificationService(config)
        
        if notification_service:
            print("   ✅ Notification service initialized successfully")
            print(f"   📱 WhatsApp notifier: {'✅' if notification_service.whatsapp_notifier else '❌'}")
            print(f"   📨 SMS service: {'✅' if notification_service.sms_service else '❌'}")
            print(f"   📞 Twilio caller: {'✅' if notification_service.twilio_caller else '❌'}")
            return True
        else:
            print("   ❌ Failed to initialize notification service")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False

def test_database_models():
    """Test database models for voice calls and SMS"""
    print("\n🗄️ Testing Database Models...")
    
    try:
        from app.models import VoiceCall, SMSNotification, EmailLog
        
        print("   ✅ VoiceCall model imported successfully")
        print("   ✅ SMSNotification model imported successfully")
        print("   ✅ EmailLog model imported successfully")
        
        # Test model attributes
        voice_call_attrs = ['id', 'email_log_id', 'recipient', 'employee_name', 'status', 'vapi_call_id']
        sms_attrs = ['id', 'email_log_id', 'recipient', 'employee_name', 'message', 'status', 'message_id']
        
        for attr in voice_call_attrs:
            if hasattr(VoiceCall, attr):
                print(f"   ✅ VoiceCall.{attr}")
            else:
                print(f"   ❌ VoiceCall.{attr} missing")
                
        for attr in sms_attrs:
            if hasattr(SMSNotification, attr):
                print(f"   ✅ SMSNotification.{attr}")
            else:
                print(f"   ❌ SMSNotification.{attr} missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Twilio Voice and SMS Integration Test")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_twilio_config),
        ("Twilio Caller", test_twilio_caller),
        ("Twilio SMS", test_twilio_sms),
        ("Notification Service", test_notification_service),
        ("Database Models", test_database_models)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Twilio integration is ready.")
    else:
        print("⚠️ Some tests failed. Please check your Twilio configuration.")

if __name__ == "__main__":
    main()
