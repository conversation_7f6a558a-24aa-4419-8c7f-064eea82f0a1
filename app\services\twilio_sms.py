"""
Twilio SMS API integration for sending SMS notifications to employees.
"""

import logging
import requests
from typing import Dict, List
from datetime import datetime
from sqlalchemy.orm import Session
from ..models import EmailLog, SMSNotification
from twilio.rest import Client
from twilio.base.exceptions import TwilioException

logger = logging.getLogger(__name__)


class TwilioSMS:
    """
    Twilio SMS API integration for sending SMS notifications to employees.
    """
    
    def __init__(self, 
                 account_sid: str,
                 auth_token: str,
                 from_number: str = "EmailAgent"):
        """
        Initialize the Twilio SMS service.
        
        Args:
            account_sid: Twilio Account SID
            auth_token: T<PERSON><PERSON> Auth Token
            from_number: SMS sender phone number
        """
        self.account_sid = account_sid
        self.auth_token = auth_token
        self.from_number = from_number
        self.client = Client(account_sid, auth_token)
        
    def send_sms(self, 
                 recipient_phone: str, 
                 message: str, 
                 email_log: EmailLog, 
                 employee_name: str = None) -> Dict:
        """
        Send SMS to a specific phone number using Twilio.
        
        Args:
            recipient_phone: Phone number to send SMS to (with country code)
            message: SMS message content (will be formatted if empty)
            email_log: EmailLog instance containing email details
            employee_name: Name of the employee receiving the SMS
            
        Returns:
            Dict: SMS response with message_id and status
        """
        try:
            # Format the SMS message if not provided
            if not message:
                formatted_message = self._format_sms_message(email_log, employee_name)
            else:
                formatted_message = message
            
            logger.info(f"Sending SMS to {recipient_phone} for employee {employee_name}")
            
            # Send SMS using Twilio client
            message_obj = self.client.messages.create(
                body=formatted_message,
                from_=self.from_number,
                to=recipient_phone
            )
            
            logger.info(f"Twilio SMS sent successfully: {message_obj.sid}")
            return {
                'success': True,
                'message_id': message_obj.sid,
                'status': message_obj.status,
                'to': message_obj.to,
                'from': message_obj.from_,
                'price': message_obj.price,
                'price_unit': message_obj.price_unit
            }
                
        except TwilioException as e:
            error_msg = f"Twilio SMS error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'error_code': getattr(e, 'code', None),
                'error_status': getattr(e, 'status', None)
            }
        except Exception as e:
            error_msg = f"Unexpected SMS error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    def send_notifications(self, email_log: EmailLog, db_session: Session) -> List[SMSNotification]:
        """
        Send SMS notifications to all active management employees.
        
        Args:
            email_log: EmailLog instance
            db_session: Database session
            
        Returns:
            List[SMSNotification]: List of SMS notification records
        """
        from .employee_service import get_employee_service

        notifications = []

        # Get active employees from shared service
        employee_service = get_employee_service()
        active_employees = employee_service.get_active_employees()
        
        if not active_employees:
            logger.warning("No active employees found for SMS notifications")
            return notifications
            
        logger.info(f"Sending SMS notifications to {len(active_employees)} active employees")
        
        for employee in active_employees:
            try:
                # Send SMS
                result = self.send_sms(
                    recipient_phone=employee['phone'],
                    message="",  # Will be formatted in send_sms method
                    email_log=email_log,
                    employee_name=employee['name']
                )
                
                # Create SMS notification record
                notification = SMSNotification(
                    email_log_id=email_log.id,
                    recipient=employee['phone'],
                    employee_name=employee['name'],
                    message=self._format_sms_message(email_log, employee['name']),
                    status='sent' if result['success'] else 'failed',
                    error_message=result.get('error'),
                    message_id=result.get('message_id'),
                    sent_at=datetime.now() if result['success'] else None
                )
                
                db_session.add(notification)
                notifications.append(notification)
                
                if result['success']:
                    logger.info(f"SMS sent to {employee['name']} ({employee['phone']}): {result['message_id']}")
                else:
                    logger.error(f"SMS failed for {employee['name']} ({employee['phone']}): {result.get('error')}")
                    
            except Exception as e:
                logger.error(f"Error sending SMS to {employee['name']}: {str(e)}")
                
                # Create failed notification record
                notification = SMSNotification(
                    email_log_id=email_log.id,
                    recipient=employee['phone'],
                    employee_name=employee['name'],
                    message=self._format_sms_message(email_log, employee['name']),
                    status='failed',
                    error_message=str(e),
                    message_id=None,
                    sent_at=None
                )
                
                db_session.add(notification)
                notifications.append(notification)
        
        # Commit all notifications
        try:
            db_session.commit()
            logger.info(f"Created {len(notifications)} SMS notification records")
        except Exception as e:
            logger.error(f"Error saving SMS notifications: {str(e)}")
            db_session.rollback()
        
        return notifications

    def _format_sms_message(self, email_log: EmailLog, employee_name: str = None) -> str:
        """
        Format the SMS message content using the same format as WhatsApp.

        Args:
            email_log: EmailLog instance
            employee_name: Employee name for personalization

        Returns:
            str: Formatted SMS message
        """
        # Extract sender name (remove email if present)
        sender_name = email_log.sender.split('<')[0].strip() if '<' in email_log.sender else email_log.sender

        # Use the same format as WhatsApp but adapted for SMS
        message = f"📧 New Email Summary\n"
        message += f"From: {sender_name}\n"
        message += f"Subject: {email_log.subject}\n\n"

        # Add the AI-generated WhatsApp summary (which is perfect for SMS too)
        if email_log.whatsapp_summary:
            message += email_log.whatsapp_summary
        else:
            # Fallback if no AI summary is available
            message += f"New email received from {sender_name}"
            if email_log.subject:
                message += f" with subject: {email_log.subject}"

        return message


class MockSMSService:
    """
    Mock SMS service for testing and development.
    """
    
    def __init__(self, *args, **kwargs):
        logger.info("Using Mock SMS Service")
        
    def send_sms(self, recipient_phone: str, message: str, email_log: EmailLog, employee_name: str = None) -> Dict:
        """Mock SMS sending"""
        logger.info(f"MOCK SMS to {recipient_phone} ({employee_name}): {message[:50]}...")
        return {
            'success': True,
            'message_id': f'mock_sms_{datetime.now().timestamp()}',
            'status': 'sent'
        }
        
    def send_notifications(self, email_log: EmailLog, db_session: Session) -> List:
        """Mock SMS notifications"""
        logger.info(f"MOCK SMS notifications for email {email_log.id}")
        return []


def get_sms_service(config: Dict) -> TwilioSMS:
    """
    Factory function to get the SMS service based on configuration.
    
    Args:
        config: Configuration dictionary containing:
            - sms_enabled: Whether SMS is enabled
            - twilio_account_sid: Twilio Account SID
            - twilio_auth_token: Twilio Auth Token
            - twilio_sms_from_number: SMS sender phone number
        
    Returns:
        TwilioSMS: SMS service instance
    """
    if not config.get('sms_enabled', False):
        return MockSMSService()
        
    return TwilioSMS(
        account_sid=config['twilio_account_sid'],
        auth_token=config['twilio_auth_token'],
        from_number=config.get('twilio_sms_from_number', 'EmailAgent')
    )
