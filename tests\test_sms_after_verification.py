#!/usr/bin/env python3
"""
Test SMS after verification/upgrade
"""

import sys
import os
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables
load_dotenv()

def test_sms_to_vishnu():
    """Test SMS specifically to <PERSON>'s number"""
    print("🧪 Testing SMS to <PERSON> After Verification")
    print("=" * 50)
    
    try:
        from twilio.rest import Client
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        from_number = os.getenv('TWILIO_SMS_FROM_NUMBER')
        
        client = Client(account_sid, auth_token)
        
        # Create the same message format as WhatsApp
        message_text = """📧 New Email Summary
From: Vishnu Bala Guru M
Subject: Urgent

Urgent help needed to fix communication issues affecting customer interactions. The problem is causing a perception of lack of follow-up from our end."""
        
        vishnu_number = "+************"
        
        print(f"📤 Sending to: {vishnu_number}")
        print(f"📝 Message preview:")
        print(f"   {message_text[:100]}...")
        
        message = client.messages.create(
            body=message_text,
            from_=from_number,
            to=vishnu_number
        )
        
        print(f"\n✅ SMS sent successfully!")
        print(f"🆔 Message ID: {message.sid}")
        print(f"📊 Status: {message.status}")
        print(f"📞 To: {message.to}")
        print(f"📞 From: {message.from_}")
        
        # Wait and check delivery
        import time
        print(f"\n⏳ Checking delivery status in 10 seconds...")
        time.sleep(10)
        
        updated_message = client.messages(message.sid).fetch()
        print(f"📊 Updated Status: {updated_message.status}")
        
        if updated_message.status == 'delivered':
            print(f"🎉 SMS delivered successfully!")
        elif updated_message.status == 'sent':
            print(f"📤 SMS sent, awaiting delivery confirmation")
        elif updated_message.error_code:
            print(f"❌ Error: {updated_message.error_code}")
        
        return True
        
    except Exception as e:
        error_str = str(e)
        if "exceeded the 9 daily messages limit" in error_str:
            print(f"❌ Still hitting daily limit - need to upgrade account")
        elif "unverified" in error_str.lower():
            print(f"❌ Number still not verified - please verify in Twilio console")
        else:
            print(f"❌ Error: {error_str}")
        return False

def check_account_status():
    """Check if account is upgraded"""
    print("🔍 Checking Account Status")
    print("=" * 30)
    
    try:
        from twilio.rest import Client
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        client = Client(account_sid, auth_token)
        
        account = client.api.accounts(account_sid).fetch()
        print(f"📊 Account Type: {account.type}")
        print(f"📊 Account Status: {account.status}")
        
        if account.type == 'Trial':
            print(f"⚠️ Still a trial account - consider upgrading")
        else:
            print(f"✅ Upgraded account - no restrictions!")
            
        return account.type != 'Trial'
        
    except Exception as e:
        print(f"❌ Error checking account: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 SMS Test After Verification/Upgrade")
    print("=" * 60)
    
    # Check account status
    is_upgraded = check_account_status()
    
    # Test SMS
    sms_success = test_sms_to_vishnu()
    
    print(f"\n🎯 Test Results")
    print("=" * 30)
    
    if sms_success:
        print(f"✅ SMS test successful!")
        print(f"📱 Check your phone (+************) for the message")
        print(f"📧 You should receive the same format as WhatsApp")
    else:
        print(f"❌ SMS test failed")
        if not is_upgraded:
            print(f"💡 Solution: Upgrade your Twilio account")
        else:
            print(f"💡 Solution: Verify your phone number in Twilio console")

if __name__ == "__main__":
    main()
