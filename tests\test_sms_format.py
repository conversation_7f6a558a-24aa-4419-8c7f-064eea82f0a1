#!/usr/bin/env python3
"""
Test the new SMS formatting to match WhatsApp format
"""

import sys
import os
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables
load_dotenv()

from app.services.twilio_sms import TwilioSMS
from app.models import EmailLog
from datetime import datetime

def test_sms_formatting():
    """Test the new SMS formatting"""
    print("🧪 Testing New SMS Format (Same as WhatsApp)")
    print("=" * 60)
    
    # Create a mock email log similar to your CIBIL email
    email_log = EmailLog(
        message_id="test_message_123",
        sender="<PERSON>la Guru M <<EMAIL>>",
        recipient="<EMAIL>",
        subject="CIBIL !!!!",
        status="processed",
        whatsapp_summary="<PERSON> sent an email with advice on blocking unwanted commercial communications and reporting fraudulent activities. He also promoted the CIBIL App for accessing CIBIL Score & Report."
    )
    
    # Initialize Twilio SMS service
    config = {
        'twilio_account_sid': os.getenv('TWILIO_ACCOUNT_SID'),
        'twilio_auth_token': os.getenv('TWILIO_AUTH_TOKEN'),
        'twilio_sms_from_number': os.getenv('TWILIO_SMS_FROM_NUMBER')
    }
    
    sms_service = TwilioSMS(
        account_sid=config['twilio_account_sid'],
        auth_token=config['twilio_auth_token'],
        from_number=config['twilio_sms_from_number']
    )
    
    # Test the formatting
    formatted_message = sms_service._format_sms_message(email_log, "John Doe")
    
    print("📱 Formatted SMS Message:")
    print("-" * 40)
    print(formatted_message)
    print("-" * 40)
    
    print(f"\n📊 Message Length: {len(formatted_message)} characters")
    
    # Test without WhatsApp summary
    email_log_no_summary = EmailLog(
        message_id="test_message_456",
        sender="Test Sender <<EMAIL>>",
        recipient="<EMAIL>",
        subject="Test Subject",
        status="processed",
        whatsapp_summary=None  # No AI summary
    )
    
    formatted_fallback = sms_service._format_sms_message(email_log_no_summary, "Jane Doe")
    
    print("\n📱 Fallback SMS Message (No AI Summary):")
    print("-" * 40)
    print(formatted_fallback)
    print("-" * 40)
    
    print(f"\n📊 Fallback Message Length: {len(formatted_fallback)} characters")
    
    return formatted_message

def test_send_formatted_sms():
    """Test sending the formatted SMS to verified number"""
    print("\n🚀 Testing SMS Send with New Format")
    print("=" * 60)
    
    # Get Twilio credentials
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    from_number = os.getenv('TWILIO_SMS_FROM_NUMBER')
    
    if not all([account_sid, auth_token, from_number]):
        print("❌ Twilio credentials not configured")
        return False
    
    # Create SMS service
    sms_service = TwilioSMS(
        account_sid=account_sid,
        auth_token=auth_token,
        from_number=from_number
    )
    
    # Create test email log
    email_log = EmailLog(
        message_id=f"test_format_{int(datetime.now().timestamp())}",
        sender="Vishnu Bala Guru M <<EMAIL>>",
        recipient="<EMAIL>",
        subject="CIBIL !!!!",
        status="processed",
        whatsapp_summary="Vishnu sent an email with advice on blocking unwanted commercial communications and reporting fraudulent activities. He also promoted the CIBIL App for accessing CIBIL Score & Report."
    )
    
    # Send to verified number (John Doe)
    verified_number = "+************"
    
    print(f"📤 Sending formatted SMS to {verified_number}...")
    
    try:
        result = sms_service.send_sms(
            recipient_phone=verified_number,
            message="",  # Will use formatted message
            email_log=email_log,
            employee_name="John Doe"
        )
        
        if result['success']:
            print(f"✅ SMS sent successfully!")
            print(f"📊 Message ID: {result['message_id']}")
            print(f"📊 Status: {result['status']}")
            print(f"💰 Price: {result.get('price')} {result.get('price_unit')}")
            print(f"📱 Check phone {verified_number} for the new formatted message!")
            return True
        else:
            print(f"❌ SMS failed: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🎯 SMS Format Test - WhatsApp Style")
    print("=" * 70)
    
    # Test 1: Format testing
    formatted_message = test_sms_formatting()
    
    # Test 2: Send test SMS
    send_success = test_send_formatted_sms()
    
    print(f"\n🎯 Test Summary")
    print("=" * 50)
    print(f"✅ Message formatting: Working")
    print(f"{'✅' if send_success else '❌'} SMS sending: {'Success' if send_success else 'Failed'}")
    
    if send_success:
        print(f"\n🎉 Success! Check John's phone (+************) for the new formatted SMS!")
        print(f"📱 The SMS should now look exactly like the WhatsApp message format")
    else:
        print(f"\n⚠️ SMS sending failed, but formatting is working correctly")

if __name__ == "__main__":
    main()
