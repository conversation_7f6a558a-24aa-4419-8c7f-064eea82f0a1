#!/usr/bin/env python3
"""
Test script to verify Twilio SMS API directly
"""

import sys
import os
import requests
import time
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables from .env file
load_dotenv()

def test_twilio_sms_api():
    """Test Twilio SMS API directly"""
    print("🔍 Testing Twilio SMS API Directly")
    print("=" * 50)
    
    # Get configuration from environment
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    from_number = os.getenv('TWILIO_SMS_FROM_NUMBER', 'EmailAgent')
    
    print(f"Account SID: {account_sid[:8]}..." if account_sid else "❌ Not set")
    print(f"Auth Token: {auth_token[:8]}..." if auth_token else "❌ Not set")
    print(f"From Number: {from_number}")
    
    if not account_sid or not auth_token:
        print("❌ Twilio SMS credentials not properly configured!")
        return False
    
    # Test numbers from dashboard
    test_numbers = [
        "+************",  # Vishnu
        "+************",  # John
        "+************"   # karthik
    ]
    
    print(f"\n📱 Testing SMS to {len(test_numbers)} numbers...")
    
    success_count = 0
    failed_count = 0
    
    for phone_number in test_numbers:
        print(f"\n📤 Sending SMS to {phone_number}...")
        
        try:
            from twilio.rest import Client
            
            client = Client(account_sid, auth_token)
            
            message = client.messages.create(
                body=f"Test SMS from Email Monitor Agent. Time: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                from_=from_number,
                to=phone_number
            )
            
            print(f"   📊 Message SID: {message.sid}")
            print(f"   📊 Status: {message.status}")
            print(f"   📊 Direction: {message.direction}")
            print(f"   💰 Price: {message.price} {message.price_unit}")
            
            if message.sid:
                print(f"   ✅ SMS sent successfully!")
                success_count += 1
            else:
                print(f"   ❌ SMS failed!")
                failed_count += 1
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
            failed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   ✅ Successful: {success_count}")
    print(f"   ❌ Failed: {failed_count}")
    print(f"   📊 Success Rate: {success_count/(success_count+failed_count)*100:.1f}%")
    
    return success_count > 0

def test_twilio_account_balance():
    """Test Twilio account balance"""
    print("\n💰 Testing Twilio Account Balance...")
    
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    
    if not account_sid or not auth_token:
        print("   ❌ Twilio credentials not configured")
        return False
    
    try:
        from twilio.rest import Client
        
        client = Client(account_sid, auth_token)
        account = client.api.accounts(account_sid).fetch()
        
        print(f"   📊 Account Status: {account.status}")
        print(f"   📊 Account Type: {account.type}")
        print(f"   📊 Account SID: {account.sid}")
        
        # Get balance
        balance = client.balance.fetch()
        print(f"   💰 Balance: {balance.balance} {balance.currency}")
        
        if float(balance.balance) > 0:
            print("   ✅ Account has sufficient balance")
            return True
        else:
            print("   ⚠️ Account balance is low or zero")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking balance: {str(e)}")
        return False

def test_twilio_pricing():
    """Test Twilio SMS pricing for target countries"""
    print("\n💰 Testing Twilio SMS Pricing...")
    
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    
    if not account_sid or not auth_token:
        print("   ❌ Twilio credentials not configured")
        return
    
    try:
        from twilio.rest import Client
        
        client = Client(account_sid, auth_token)
        
        # Check pricing for India (where our test numbers are)
        countries = ['IN']  # India
        
        for country_code in countries:
            try:
                pricing = client.pricing.v1.messaging.countries(country_code).fetch()
                print(f"   📊 Country: {pricing.country} ({country_code})")
                print(f"   💰 Price Unit: {pricing.price_unit}")
                
                if pricing.outbound_sms_prices:
                    for price_info in pricing.outbound_sms_prices:
                        print(f"      📱 SMS Price: {price_info['prices'][0]['current_price']} {pricing.price_unit}")
                        break
                        
            except Exception as e:
                print(f"   ❌ Error getting pricing for {country_code}: {str(e)}")
                
    except Exception as e:
        print(f"   ❌ Error checking pricing: {str(e)}")

def main():
    """Main function"""
    print("🚀 Twilio SMS Direct Test")
    print("=" * 50)
    
    # Test account balance first
    balance_ok = test_twilio_account_balance()
    
    # Check pricing
    test_twilio_pricing()
    
    # Test SMS sending
    if balance_ok:
        sms_success = test_twilio_sms_api()
        
        if sms_success:
            print("\n🎉 SMS Test Successful!")
            print("   📱 Check your phones for received messages")
        else:
            print("\n❌ SMS Test Failed!")
            print("   🔧 Please check your Twilio configuration")
    else:
        print("\n⚠️  Skipping SMS test due to account balance issues")

if __name__ == "__main__":
    main()
