#!/usr/bin/env python3
"""
Test Twilio voice calling functionality
"""

import sys
import os
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables
load_dotenv()

def test_voice_calling_config():
    """Test voice calling configuration"""
    print("🔍 Testing Voice Calling Configuration")
    print("=" * 50)
    
    # Check environment variables
    voice_enabled = os.getenv('VOICE_CALLING_ENABLED', 'False').lower() == 'true'
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    from_number = os.getenv('TWILIO_FROM_NUMBER')
    
    print(f"📞 Voice Calling Enabled: {voice_enabled}")
    print(f"🔑 Account SID: {account_sid[:10]}..." if account_sid else "❌ Not set")
    print(f"🔑 Auth Token: {auth_token[:10]}..." if auth_token else "❌ Not set")
    print(f"📞 From Number: {from_number}")
    
    if not voice_enabled:
        print(f"\n⚠️ Voice calling is DISABLED in configuration")
        print(f"   To enable: Set VOICE_CALLING_ENABLED=True in .env")
        return False
    
    if not all([account_sid, auth_token, from_number]):
        print(f"\n❌ Missing Twilio credentials for voice calling")
        return False
    
    print(f"\n✅ Voice calling configuration looks good")
    return True

def test_twilio_caller_service():
    """Test the Twilio caller service"""
    print(f"\n📞 Testing Twilio Caller Service")
    print("=" * 50)
    
    try:
        from app.services.twilio_caller import get_twilio_caller
        
        config = {
            'voice_calling_enabled': True,
            'twilio_account_sid': os.getenv('TWILIO_ACCOUNT_SID'),
            'twilio_auth_token': os.getenv('TWILIO_AUTH_TOKEN'),
            'twilio_from_number': os.getenv('TWILIO_FROM_NUMBER')
        }
        
        caller = get_twilio_caller(config)
        
        if caller:
            print(f"✅ Twilio caller service initialized")
            print(f"📊 Service type: {type(caller).__name__}")
            
            if hasattr(caller, 'account_sid'):
                print(f"📊 Account SID: {caller.account_sid[:10]}...")
                print(f"📞 From Number: {caller.from_number}")
            else:
                print(f"⚠️ Using mock service (voice calling disabled)")
                
            return caller
        else:
            print(f"❌ Failed to initialize caller service")
            return None
            
    except Exception as e:
        print(f"❌ Error initializing caller service: {str(e)}")
        return None

def test_voice_call_to_number(caller, phone_number, name):
    """Test making a voice call to a specific number"""
    print(f"\n📞 Testing Voice Call to {name}: {phone_number}")
    print("=" * 60)
    
    if not caller or not hasattr(caller, 'make_call'):
        print(f"❌ Caller service not available")
        return False
    
    try:
        # Create a mock email log for testing
        from app.models import EmailLog
        
        test_email = EmailLog(
            message_id="voice_test_123",
            sender="Vishnu Bala Guru M <<EMAIL>>",
            recipient="<EMAIL>",
            subject="Urgent - Voice Call Test",
            status="processed",
            whatsapp_summary="This is a test voice call from the Email Monitor system. Please answer to confirm voice calling is working."
        )
        
        print(f"📤 Initiating voice call...")
        print(f"📞 To: {phone_number}")
        print(f"👤 Employee: {name}")
        
        # Make the call
        result = caller.make_call(
            recipient_phone=phone_number,
            email_log=test_email,
            employee_name=name
        )
        
        print(f"\n📊 Call Result:")
        print(f"   ✅ Success: {result.get('success', False)}")
        
        if result.get('success'):
            print(f"   🆔 Call ID: {result.get('call_id')}")
            print(f"   📊 Status: {result.get('status')}")
            print(f"   📞 To: {result.get('to')}")
            print(f"   📞 From: {result.get('from')}")
            print(f"   💰 Price: {result.get('price')} {result.get('price_unit')}")
            print(f"   🎉 Voice call initiated successfully!")
            print(f"   📱 {name} should receive a call now!")
            
            return result.get('call_id')
        else:
            print(f"   ❌ Error: {result.get('error')}")
            print(f"   🔧 Error Code: {result.get('error_code')}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during voice call: {str(e)}")
        return False

def check_call_status(caller, call_id):
    """Check the status of a voice call"""
    if not call_id or not caller or not hasattr(caller, 'get_call_status'):
        return
    
    print(f"\n📊 Checking Call Status")
    print("=" * 30)
    
    try:
        import time
        print(f"⏳ Waiting 10 seconds before checking status...")
        time.sleep(10)
        
        status_result = caller.get_call_status(call_id)
        
        if status_result.get('success'):
            print(f"✅ Call status retrieved:")
            print(f"   🆔 Call ID: {status_result.get('call_id')}")
            print(f"   📊 Status: {status_result.get('status')}")
            print(f"   ⏱️ Duration: {status_result.get('duration')} seconds")
            print(f"   💰 Price: {status_result.get('price')} {status_result.get('price_unit')}")
            print(f"   🕐 Start Time: {status_result.get('start_time')}")
            print(f"   🕐 End Time: {status_result.get('end_time')}")
        else:
            print(f"❌ Error getting call status: {status_result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error checking call status: {str(e)}")

def test_notification_service_voice():
    """Test voice calling through notification service"""
    print(f"\n🔔 Testing Voice Calling via Notification Service")
    print("=" * 60)
    
    try:
        from app.services.notification_service import get_notification_service
        from app.models import EmailLog, get_db
        from start_monitoring_improved import get_config
        
        config = get_config()
        config['voice_calling_enabled'] = True  # Force enable for test
        
        notification_service = get_notification_service(config)
        
        print(f"✅ Notification service initialized")
        print(f"📞 Voice caller available: {'✅' if notification_service.twilio_caller else '❌'}")
        
        if not notification_service.twilio_caller:
            print(f"❌ Voice calling not available in notification service")
            return False
        
        # Create test email
        db = get_db()
        test_email = EmailLog(
            message_id=f"voice_notification_test_{int(__import__('datetime').datetime.now().timestamp())}",
            sender="Vishnu Bala Guru M <<EMAIL>>",
            recipient="<EMAIL>",
            subject="Voice Notification Test",
            status="processed",
            whatsapp_summary="This is a test of voice calling through the notification service. The system should call all active employees."
        )
        
        db.add(test_email)
        db.commit()
        
        print(f"📧 Created test email: {test_email.id}")
        
        # Test voice calling
        results = notification_service.send_notifications(test_email, db)
        
        voice_calls = results.get('voice_calls', [])
        print(f"📞 Voice calls initiated: {len(voice_calls)}")
        
        for call in voice_calls:
            if hasattr(call, 'employee_name'):
                print(f"   📞 {call.employee_name} ({call.recipient}): {call.status}")
                if call.vapi_call_id:
                    print(f"      🆔 Call ID: {call.vapi_call_id}")
        
        db.close()
        return len(voice_calls) > 0
        
    except Exception as e:
        print(f"❌ Error testing notification service: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main voice calling test"""
    print("🚀 Twilio Voice Calling Test")
    print("=" * 70)
    
    # Step 1: Check configuration
    config_ok = test_voice_calling_config()
    
    if not config_ok:
        print(f"\n❌ Voice calling configuration issues found")
        print(f"💡 To enable voice calling:")
        print(f"   1. Set VOICE_CALLING_ENABLED=True in .env")
        print(f"   2. Ensure TWILIO_FROM_NUMBER is set")
        return
    
    # Step 2: Test caller service
    caller = test_twilio_caller_service()
    
    if not caller:
        print(f"\n❌ Cannot proceed without caller service")
        return
    
    # Step 3: Test individual call (to a verified number)
    test_number = "+918778869983"  # John's number (most likely to work)
    call_id = test_voice_call_to_number(caller, test_number, "John Doe")
    
    # Step 4: Check call status
    if call_id:
        check_call_status(caller, call_id)
    
    # Step 5: Test through notification service
    notification_success = test_notification_service_voice()
    
    print(f"\n🎯 Voice Calling Test Summary")
    print("=" * 50)
    print(f"✅ Configuration: {'OK' if config_ok else 'Failed'}")
    print(f"✅ Caller Service: {'OK' if caller else 'Failed'}")
    print(f"✅ Individual Call: {'OK' if call_id else 'Failed'}")
    print(f"✅ Notification Service: {'OK' if notification_success else 'Failed'}")
    
    if call_id:
        print(f"\n📱 John Doe should have received a voice call!")
        print(f"   Ask him if he got an automated call about email notification")
    
    if not call_id:
        print(f"\n💡 Voice calling might be limited by:")
        print(f"   - Twilio trial account restrictions")
        print(f"   - Unverified phone numbers")
        print(f"   - Daily usage limits")
        print(f"   - Account balance")

if __name__ == "__main__":
    main()
