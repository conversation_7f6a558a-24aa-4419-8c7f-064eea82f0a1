/**
 * Email Monitor Agent Dashboard - Main Application
 */

// Global application state
const App = {
    // API base URL
    apiUrl: 'http://localhost:8082',
    
    // Current active tab
    currentTab: 'dashboard',
    
    // Data cache
    cache: {
        emails: [],
        employees: [],
        stats: {},
        lastUpdate: null
    },
    
    // Configuration
    config: {
        refreshInterval: 30000, // 30 seconds
        itemsPerPage: 10
    },
    
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.setupTabNavigation();
        this.checkAgentStatus();
        this.loadInitialData();
        this.startAutoRefresh();
        
        console.log('📱 Email Monitor Dashboard initialized');
    },
    
    // Set up event listeners
    setupEventListeners() {
        // Navigation clicks
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const tab = link.dataset.tab;
                this.switchTab(tab);
            });
        });
        
        // Modal close events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeAllModals();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
        
        // Form submissions
        document.getElementById('employeeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleEmployeeFormSubmit();
        });
    },
    
    // Set up tab navigation
    setupTabNavigation() {
        const tabs = document.querySelectorAll('.tab-content');
        tabs.forEach(tab => tab.classList.remove('active'));
        
        // Show dashboard by default
        document.getElementById('dashboard').classList.add('active');
    },
    
    // Switch between tabs
    switchTab(tabName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
        
        this.currentTab = tabName;
        
        // Load tab-specific data
        this.loadTabData(tabName);
    },
    
    // Load data for specific tab
    loadTabData(tabName) {
        switch (tabName) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'emails':
                this.loadEmailsData();
                break;
            case 'employees':
                this.loadEmployeesData();
                break;
            case 'settings':
                this.loadSettingsData();
                break;
        }
    },
    
    // Load initial data
    loadInitialData() {
        this.loadDashboardData();
    },
    
    // Start auto-refresh
    startAutoRefresh() {
        setInterval(() => {
            if (this.currentTab === 'dashboard') {
                this.loadDashboardData();
            }
        }, this.config.refreshInterval);
    },
    
    // Check agent status
    async checkAgentStatus() {
        try {
            const response = await this.apiCall('/');
            const statusElement = document.getElementById('agentStatus');
            
            if (response.ok) {
                statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Agent Online</span>';
                statusElement.style.color = 'var(--success-color)';
            } else {
                throw new Error('Agent offline');
            }
        } catch (error) {
            const statusElement = document.getElementById('agentStatus');
            statusElement.innerHTML = '<i class="fas fa-circle"></i> <span>Agent Offline</span>';
            statusElement.style.color = 'var(--danger-color)';
        }
    },
    
    // Generic API call method
    async apiCall(endpoint, options = {}) {
        const url = `${this.apiUrl}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            return response;
        } catch (error) {
            console.error('API call failed:', error);
            this.showToast('error', 'Connection Error', 'Failed to connect to the server');
            throw error;
        }
    },
    
    // Show loading overlay
    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    },
    
    // Hide loading overlay
    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    },
    
    // Show toast notification
    showToast(type, title, message, duration = 5000) {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-title">
                    <i class="fas fa-${this.getToastIcon(type)}"></i>
                    ${title}
                </div>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="toast-message">${message}</div>
        `;
        
        container.appendChild(toast);
        
        // Auto remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, duration);
    },
    
    // Get icon for toast type
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // Close all modals
    closeAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('active');
        });
    },
    
    // Format date for display
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        // Less than 1 minute
        if (diff < 60000) {
            return 'Just now';
        }
        
        // Less than 1 hour
        if (diff < 3600000) {
            const minutes = Math.floor(diff / 60000);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        }
        
        // Less than 1 day
        if (diff < 86400000) {
            const hours = Math.floor(diff / 3600000);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        }
        
        // More than 1 day
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // Format phone number
    formatPhoneNumber(phone) {
        // Remove all non-digit characters except +
        const cleaned = phone.replace(/[^\d+]/g, '');
        
        // If it starts with +, format as international
        if (cleaned.startsWith('+')) {
            return cleaned.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1 $2 $3 $4');
        }
        
        return cleaned;
    },
    
    // Truncate text
    truncateText(text, maxLength = 100) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    },
    
    // Handle employee form submission
    async handleEmployeeFormSubmit() {
        const form = document.getElementById('employeeForm');
        const formData = new FormData(form);
        
        const employeeData = {
            name: formData.get('name') || document.getElementById('employeeName').value,
            phone: formData.get('phone') || document.getElementById('employeePhone').value
        };
        
        const employeeId = document.getElementById('employeeId').value;
        
        try {
            this.showLoading();
            
            let response;
            if (employeeId) {
                // Update existing employee
                response = await this.apiCall(`/api/employees/${employeeId}`, {
                    method: 'PUT',
                    body: JSON.stringify(employeeData)
                });
            } else {
                // Create new employee
                response = await this.apiCall('/api/employees', {
                    method: 'POST',
                    body: JSON.stringify(employeeData)
                });
            }
            
            if (response.ok) {
                this.showToast('success', 'Success', 
                    employeeId ? 'Employee updated successfully' : 'Employee added successfully');
                this.closeAllModals();
                this.loadEmployeesData();
            } else {
                const error = await response.json();
                this.showToast('error', 'Error', error.detail || 'Failed to save employee');
            }
        } catch (error) {
            this.showToast('error', 'Error', 'Failed to save employee');
        } finally {
            this.hideLoading();
        }
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// Global functions for HTML onclick handlers
function refreshDashboard() {
    App.loadDashboardData();
}

function openAddEmployeeModal() {
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-plus"></i> Add Employee';
    document.getElementById('employeeForm').reset();
    document.getElementById('employeeId').value = '';
    document.getElementById('employeeModal').classList.add('active');
}

function closeEmployeeModal() {
    document.getElementById('employeeModal').classList.remove('active');
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.remove('active');
}

function applyFilters() {
    App.loadEmailsData();
}

function clearFilters() {
    document.getElementById('dateFilter').value = 'all';
    document.getElementById('senderFilter').value = '';
    document.getElementById('statusFilter').value = 'all';
    App.loadEmailsData();
}
