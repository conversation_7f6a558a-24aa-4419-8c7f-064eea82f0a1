#!/usr/bin/env python3
"""
Simple voice calling test
"""

import os
from dotenv import load_dotenv
load_dotenv()

def test_voice_config():
    """Test voice configuration"""
    print("📞 Voice Calling Configuration")
    print("=" * 40)
    
    voice_enabled = os.getenv('VOICE_CALLING_ENABLED', 'False').lower() == 'true'
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    from_number = os.getenv('TWILIO_FROM_NUMBER')
    
    print(f"Voice Enabled: {voice_enabled}")
    print(f"Account SID: {account_sid[:10]}..." if account_sid else "❌ Missing")
    print(f"Auth Token: {auth_token[:10]}..." if auth_token else "❌ Missing")
    print(f"From Number: {from_number}")
    
    return voice_enabled and all([account_sid, auth_token, from_number])

def test_simple_call():
    """Test a simple voice call"""
    print(f"\n📞 Testing Simple Voice Call")
    print("=" * 40)
    
    try:
        from twilio.rest import Client
        from twilio.twiml.voice_response import VoiceResponse
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        from_number = os.getenv('TWILIO_FROM_NUMBER')
        
        client = Client(account_sid, auth_token)
        
        # Create TwiML for the call
        twiml = VoiceResponse()
        twiml.say("Hello! This is a test call from the Email Monitor Agent. Voice calling is working correctly. Thank you!", 
                  voice='alice', language='en-US')
        
        # Test number (John's verified number)
        test_number = "+************"
        
        print(f"📤 Making call to: {test_number}")
        print(f"📞 From: {from_number}")
        
        # Make the call
        call = client.calls.create(
            twiml=str(twiml),
            to=test_number,
            from_=from_number
        )
        
        print(f"✅ Call initiated!")
        print(f"🆔 Call SID: {call.sid}")
        print(f"📊 Status: {call.status}")
        print(f"📞 To: {call.to}")
        print(f"📞 From: {call.from_}")
        
        # Wait and check status
        import time
        print(f"\n⏳ Waiting 15 seconds to check call status...")
        time.sleep(15)
        
        # Check call status
        updated_call = client.calls(call.sid).fetch()
        print(f"📊 Updated Status: {updated_call.status}")
        print(f"⏱️ Duration: {updated_call.duration} seconds")
        print(f"💰 Price: {updated_call.price} {updated_call.price_unit}")
        
        if updated_call.status in ['completed', 'in-progress']:
            print(f"🎉 Voice call successful!")
            print(f"📱 John should have received the call!")
        elif updated_call.status == 'failed':
            print(f"❌ Call failed")
        else:
            print(f"📞 Call status: {updated_call.status}")
        
        return call.sid
        
    except Exception as e:
        error_str = str(e)
        if "exceeded" in error_str and "limit" in error_str:
            print(f"❌ Daily limit exceeded")
        elif "unverified" in error_str.lower():
            print(f"❌ Unverified number")
        else:
            print(f"❌ Error: {error_str}")
        return None

def main():
    """Main function"""
    print("🚀 Simple Voice Calling Test")
    print("=" * 50)
    
    # Test configuration
    config_ok = test_voice_config()
    
    if not config_ok:
        print(f"\n❌ Voice calling configuration issues")
        return
    
    # Test voice call
    call_sid = test_simple_call()
    
    print(f"\n🎯 Test Summary")
    print("=" * 30)
    
    if call_sid:
        print(f"✅ Voice calling is working!")
        print(f"📞 Call ID: {call_sid}")
        print(f"📱 John should have received a test call")
        print(f"🎤 Message: 'Hello! This is a test call from the Email Monitor Agent...'")
    else:
        print(f"❌ Voice calling failed")
        print(f"💡 Possible issues:")
        print(f"   - Twilio trial account limits")
        print(f"   - Unverified phone numbers")
        print(f"   - Daily usage limits")

if __name__ == "__main__":
    main()
