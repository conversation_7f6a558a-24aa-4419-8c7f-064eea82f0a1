#!/usr/bin/env python3
"""
Test SMS to all employee numbers to see which ones actually receive messages
"""

import sys
import time
import json
sys.path.append('.')

from app.services.twilio_sms import get_sms_service
from app.services.employee_service import get_employee_service
from start_monitoring_improved import get_config
from app.models import EmailLog, get_db

def test_all_employee_numbers():
    """Test SMS to all employee numbers with detailed tracking"""
    print("📱 Testing SMS to ALL Employee Numbers")
    print("=" * 60)
    
    config = get_config()
    sms_service = get_sms_service(config)
    employee_service = get_employee_service()
    
    # Get all employees from dashboard
    employees = employee_service.get_all_employees()
    
    print(f"Found {len(employees)} employees in dashboard:")
    for emp in employees:
        print(f"   - {emp['name']}: {emp['phone']} (ID: {emp['id']})")
    
    # Create test email
    db = get_db()
    test_email = EmailLog(
        message_id=f"all-numbers-test-{int(time.time())}",
        sender="<EMAIL>",
        recipient="<EMAIL>",
        subject="All Numbers SMS Test",
        status="received"
    )
    db.add(test_email)
    db.commit()
    
    print(f"\n🧪 Sending Test SMS to All Numbers:")
    print("=" * 60)
    
    results = []
    test_time = time.strftime('%H:%M:%S')
    
    for i, emp in enumerate(employees, 1):
        print(f"\n📱 {i}. Testing {emp['name']}: {emp['phone']}")
        print("-" * 50)
        
        # Create unique message for each person
        message = f"🧪 SMS Test #{i} for {emp['name']} at {test_time}. Please reply 'GOT IT' if you receive this message!"
        
        try:
            # Send SMS
            result = sms_service.send_sms(
                recipient_phone=emp['phone'],
                message=message,
                email_log=test_email,
                employee_name=emp['name']
            )
            
            # Store result
            test_result = {
                'employee_name': emp['name'],
                'phone': emp['phone'],
                'employee_id': emp['id'],
                'success': result.get('success', False),
                'message_id': result.get('message_id'),
                'error': result.get('error'),
                'test_time': test_time,
                'message_sent': message
            }
            results.append(test_result)
            
            print(f"   📤 SMS Status:")
            print(f"      ✅ Success: {result.get('success', False)}")
            print(f"      🆔 Message ID: {result.get('message_id', 'None')}")
            print(f"      ❌ Error: {result.get('error', 'None')}")
            print(f"      💬 Message: {message[:50]}...")
            
            if result.get('success'):
                print(f"      🎉 SMS API call successful!")
                print(f"      📱 Check phone {emp['phone']} for the message")
            else:
                print(f"      💥 SMS API call failed!")
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
            test_result = {
                'employee_name': emp['name'],
                'phone': emp['phone'],
                'employee_id': emp['id'],
                'success': False,
                'message_id': None,
                'error': str(e),
                'test_time': test_time,
                'message_sent': message
            }
            results.append(test_result)
    
    # Summary
    print(f"\n📊 SMS Test Summary")
    print("=" * 60)
    
    successful_sends = [r for r in results if r['success']]
    failed_sends = [r for r in results if not r['success']]
    
    print(f"✅ Successful API calls: {len(successful_sends)}/{len(results)}")
    print(f"❌ Failed API calls: {len(failed_sends)}/{len(results)}")
    
    if successful_sends:
        print(f"\n✅ SMS sent successfully to:")
        for result in successful_sends:
            print(f"   - {result['employee_name']}: {result['phone']}")
            print(f"     Message ID: {result['message_id']}")
    
    if failed_sends:
        print(f"\n❌ SMS failed for:")
        for result in failed_sends:
            print(f"   - {result['employee_name']}: {result['phone']}")
            print(f"     Error: {result['error']}")
    
    # Save detailed results to file
    with open('sms_test_results.json', 'w') as f:
        json.dump({
            'test_time': test_time,
            'total_employees': len(employees),
            'successful_sends': len(successful_sends),
            'failed_sends': len(failed_sends),
            'results': results
        }, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: sms_test_results.json")
    
    # Instructions for verification
    print(f"\n📋 Verification Instructions:")
    print("=" * 60)
    print(f"1. Check ALL phones for SMS messages from 'EmailAgent'")
    print(f"2. Each person should receive a unique message:")
    for i, result in enumerate(successful_sends, 1):
        print(f"   {i}. {result['employee_name']} ({result['phone']}): 'SMS Test #{i} for {result['employee_name']}...'")
    
    print(f"\n3. If you receive the message, reply 'GOT IT' to confirm")
    print(f"4. Check spam/blocked folders if not in main inbox")
    print(f"5. Wait 5-10 minutes for potential network delays")
    
    print(f"\n🎯 Expected Results:")
    print(f"   - All {len(successful_sends)} numbers should receive SMS")
    print(f"   - If only John (+918778869983) receives it, it's a network/carrier issue")
    print(f"   - If multiple numbers receive it, the system is working perfectly")
    
    db.close()
    return results

def test_with_twilio_direct():
    """Test using Twilio API directly for comparison"""
    print(f"\n🔧 Testing with Direct Twilio API")
    print("=" * 60)

    try:
        from twilio.rest import Client
        config = get_config()
        employee_service = get_employee_service()
        employees = employee_service.get_all_employees()

        client = Client(
            config['twilio_account_sid'],
            config['twilio_auth_token']
        )

        test_time = time.strftime('%H:%M:%S')

        for i, emp in enumerate(employees, 1):
            print(f"\n📱 Direct API Test {i}: {emp['name']} ({emp['phone']})")

            try:
                message = client.messages.create(
                    body=f"🔧 Direct API Test #{i} for {emp['name']} at {test_time}. This bypasses our SMS service.",
                    from_=config.get('twilio_sms_from_number', 'EmailAgent'),
                    to=emp['phone']
                )

                print(f"   📤 Twilio Response: {message.sid}")
                print(f"   ✅ Direct API Success! Message ID: {message.sid}")
                print(f"   📊 Status: {message.status}")
                print(f"   💰 Price: {message.price} {message.price_unit}")

            except Exception as e:
                print(f"   ❌ Direct API Exception: {str(e)}")

    except ImportError:
        print("   ⚠️ Twilio library not available for direct testing")
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Comprehensive SMS Test for All Employee Numbers")
    print("=" * 70)
    print("This will send test SMS to ALL employees in your dashboard")
    print("=" * 70)
    
    # Test 1: Using our SMS service
    results = test_all_employee_numbers()
    
    # Test 2: Using direct Twilio API
    test_with_twilio_direct()
    
    print(f"\n🎯 Final Summary")
    print("=" * 50)
    print(f"✅ SMS system is technically working")
    print(f"📱 Check ALL phones for messages")
    print(f"⏰ Wait up to 10 minutes for delivery")
    print(f"📂 Check spam folders if needed")
    print(f"💬 Reply 'GOT IT' if you receive any message")
    
    successful_count = len([r for r in results if r['success']])
    print(f"\n📊 API Success Rate: {successful_count}/{len(results)} ({successful_count/len(results)*100:.1f}%)")

if __name__ == "__main__":
    main()
