#!/usr/bin/env python3
"""
Debug SMS notification issue to find out why SMS is not being sent
"""

import sys
import os
import time
sys.path.append('.')

from app.models import EmailLog, get_db
from start_monitoring_improved import get_config
from app.services.notification_service import get_notification_service
from app.services.twilio_sms import get_sms_service
from app.services.ai_summarizer import get_ai_summarizer

def debug_config():
    """Debug the configuration to see SMS settings"""
    print("🔧 Debugging Configuration")
    print("=" * 50)
    
    config = get_config()
    
    print(f"SMS Configuration:")
    print(f"   📨 sms_enabled: {config.get('sms_enabled')}")
    print(f"   🔑 vonage_sms_api_key: {'✅ Set' if config.get('vonage_sms_api_key') else '❌ Missing'}")
    print(f"   🔑 vonage_sms_api_secret: {'✅ Set' if config.get('vonage_sms_api_secret') else '❌ Missing'}")
    print(f"   📱 vonage_sms_from_number: {config.get('vonage_sms_from_number', 'Not set')}")
    
    print(f"\nWhatsApp Configuration:")
    print(f"   📱 mock_whatsapp: {config.get('mock_whatsapp')}")
    print(f"   🔑 meta_api_token: {'✅ Set' if config.get('meta_api_token') else '❌ Missing'}")
    print(f"   🔑 meta_phone_number_id: {'✅ Set' if config.get('meta_phone_number_id') else '❌ Missing'}")
    
    return config

def debug_sms_service():
    """Debug the SMS service initialization"""
    print("\n📨 Debugging SMS Service")
    print("=" * 50)
    
    try:
        config = get_config()
        sms_service = get_sms_service(config)
        
        print(f"SMS Service Type: {type(sms_service).__name__}")
        
        if hasattr(sms_service, 'api_key'):
            print(f"   🔑 API Key: {'✅ Set' if sms_service.api_key else '❌ Missing'}")
            print(f"   🔑 API Secret: {'✅ Set' if sms_service.api_secret else '❌ Missing'}")
            print(f"   📱 From Number: {sms_service.from_number}")
        else:
            print(f"   ⚠️ This is a Mock SMS Service - SMS won't be sent!")
        
        return sms_service
        
    except Exception as e:
        print(f"   ❌ Error initializing SMS service: {e}")
        return None

def debug_notification_service():
    """Debug the notification service"""
    print("\n📱 Debugging Notification Service")
    print("=" * 50)
    
    try:
        config = get_config()
        notification_service = get_notification_service(config)
        
        print(f"Notification Service Type: {type(notification_service).__name__}")
        print(f"   📱 WhatsApp Service: {type(notification_service.whatsapp_notifier).__name__}")
        print(f"   📨 SMS Service: {type(notification_service.sms_service).__name__}")
        print(f"   📞 Voice Service: {'✅ Enabled' if notification_service.vonage_caller else '❌ Disabled'}")
        
        return notification_service
        
    except Exception as e:
        print(f"   ❌ Error initializing notification service: {e}")
        return None

def debug_employee_data():
    """Debug employee data retrieval"""
    print("\n👥 Debugging Employee Data")
    print("=" * 50)
    
    try:
        config = get_config()
        notification_service = get_notification_service(config)
        
        # Test getting employees
        employees = notification_service._get_active_employees(None)
        
        print(f"Active Employees Found: {len(employees)}")
        for emp in employees:
            print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
        
        return employees
        
    except Exception as e:
        print(f"   ❌ Error getting employee data: {e}")
        return []

def test_sms_service_directly():
    """Test SMS service directly"""
    print("\n🧪 Testing SMS Service Directly")
    print("=" * 50)
    
    try:
        config = get_config()
        sms_service = get_sms_service(config)
        
        if type(sms_service).__name__ == 'MockSMSService':
            print("   ⚠️ SMS Service is in MOCK mode - no real SMS will be sent")
            return False
        
        # Create a test email log
        db = get_db()
        test_email = EmailLog(
            message_id=f"debug-test-{int(time.time())}",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="Debug SMS Test",
            status="received"
        )
        db.add(test_email)
        db.commit()
        
        # Test sending SMS to one number
        test_phone = "+917598638873"  # Vishnu's number
        result = sms_service.send_sms(
            recipient_phone=test_phone,
            message="Debug test SMS",
            email_log=test_email,
            employee_name="Debug Test"
        )
        
        print(f"SMS Test Result:")
        print(f"   📱 To: {test_phone}")
        print(f"   ✅ Success: {result.get('success', False)}")
        print(f"   🆔 Message ID: {result.get('message_id', 'None')}")
        print(f"   ❌ Error: {result.get('error', 'None')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"   ❌ Error testing SMS service: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_notification_flow():
    """Test the full notification flow"""
    print("\n🔄 Testing Full Notification Flow")
    print("=" * 50)
    
    try:
        config = get_config()
        db = get_db()
        
        # Create test email
        test_email = EmailLog(
            message_id=f"debug-flow-{int(time.time())}",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="Debug Notification Flow Test",
            status="received"
        )
        db.add(test_email)
        db.commit()
        
        # AI processing (required for notifications)
        print("   🤖 Running AI processing...")
        ai_summarizer = get_ai_summarizer(config)
        email_content = "This is a debug test to check SMS notifications."
        ai_result = ai_summarizer.process_email(test_email, email_content, db)
        
        if not ai_result.get('success'):
            print(f"   ❌ AI processing failed: {ai_result.get('error')}")
            return False
        
        print(f"   ✅ AI processing completed")
        
        # Send notifications
        print("   📱 Sending notifications...")
        notification_service = get_notification_service(config)
        results = notification_service.send_notifications(test_email, db)
        
        print(f"Notification Results:")
        print(f"   📱 WhatsApp: {len(results.get('whatsapp_notifications', []))} sent")
        print(f"   📨 SMS: {len(results.get('sms_notifications', []))} sent")
        print(f"   📞 Voice: {len(results.get('voice_calls', []))} sent")
        print(f"   ✅ Success: {results.get('success_count', 0)}")
        print(f"   ❌ Failed: {results.get('failure_count', 0)}")
        
        # Show detailed SMS results
        sms_notifications = results.get('sms_notifications', [])
        if sms_notifications:
            print(f"\n📨 SMS Details:")
            for sms in sms_notifications:
                print(f"   - {sms['employee_name']} ({sms['recipient']}): {sms['status']}")
                if sms.get('message_id'):
                    print(f"     Message ID: {sms['message_id']}")
                if sms.get('error_message'):
                    print(f"     Error: {sms['error_message']}")
        else:
            print(f"\n⚠️ No SMS notifications were sent!")
        
        return len(sms_notifications) > 0
        
    except Exception as e:
        print(f"   ❌ Error in notification flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🔍 SMS Notification Debug Tool")
    print("=" * 60)
    
    # Step 1: Check configuration
    config = debug_config()
    
    # Step 2: Check SMS service
    sms_service = debug_sms_service()
    
    # Step 3: Check notification service
    notification_service = debug_notification_service()
    
    # Step 4: Check employee data
    employees = debug_employee_data()
    
    # Step 5: Test SMS service directly
    sms_direct_success = test_sms_service_directly()
    
    # Step 6: Test full notification flow
    flow_success = test_full_notification_flow()
    
    # Summary
    print(f"\n🎯 Debug Summary")
    print("=" * 50)
    print(f"   🔧 Configuration: {'✅' if config.get('sms_enabled') else '❌'}")
    print(f"   📨 SMS Service: {'✅' if sms_service and type(sms_service).__name__ != 'MockSMSService' else '❌'}")
    print(f"   📱 Notification Service: {'✅' if notification_service else '❌'}")
    print(f"   👥 Employee Data: {'✅' if employees else '❌'}")
    print(f"   🧪 Direct SMS Test: {'✅' if sms_direct_success else '❌'}")
    print(f"   🔄 Full Flow Test: {'✅' if flow_success else '❌'}")
    
    if not config.get('sms_enabled'):
        print(f"\n❌ ISSUE FOUND: SMS is disabled in configuration!")
        print(f"   💡 Set SMS_ENABLED=True in your .env file")
    elif type(sms_service).__name__ == 'MockSMSService':
        print(f"\n❌ ISSUE FOUND: SMS service is in MOCK mode!")
        print(f"   💡 Check your Vonage API credentials in .env file")
    elif not employees:
        print(f"\n❌ ISSUE FOUND: No active employees found!")
        print(f"   💡 Check employee data in the dashboard")
    elif not flow_success:
        print(f"\n❌ ISSUE FOUND: SMS notifications not working in full flow!")
        print(f"   💡 Check the detailed error messages above")
    else:
        print(f"\n🎉 SMS notifications should be working!")

if __name__ == "__main__":
    main()
