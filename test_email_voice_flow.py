#!/usr/bin/env python3
"""
Test the complete email processing flow to see if voice calls are triggered
"""

import sys
import os
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables
load_dotenv()

def test_email_processing_with_voice():
    """Test email processing to see if voice calls are triggered"""
    print("🧪 Testing Email Processing with Voice Calls")
    print("=" * 60)
    
    try:
        from app.worker import process_email
        from app.models import EmailLog, get_db, VoiceCall
        from datetime import datetime
        
        # Create a test email
        db = get_db()
        
        test_email = EmailLog(
            message_id=f"voice_test_{int(datetime.now().timestamp())}",
            sender="Vishnu Bala Guru M <<EMAIL>>",
            recipient="<EMAIL>",
            subject="Voice Call Test Email",
            body="This is a test email to check if voice calls are triggered during email processing.",
            status="new",
            received_at=datetime.now()
        )
        
        db.add(test_email)
        db.commit()
        
        print(f"📧 Created test email: {test_email.id}")
        print(f"📞 Voice calling enabled: {os.getenv('VOICE_CALLING_ENABLED')}")
        
        # Check voice calls before processing
        voice_calls_before = db.query(VoiceCall).count()
        print(f"📊 Voice calls before: {voice_calls_before}")
        
        # Process the email
        print(f"\n📤 Processing email through complete pipeline...")
        process_email(test_email.id)
        
        # Check voice calls after processing
        voice_calls_after = db.query(VoiceCall).count()
        print(f"📊 Voice calls after: {voice_calls_after}")
        
        new_voice_calls = voice_calls_after - voice_calls_before
        print(f"📞 New voice calls created: {new_voice_calls}")
        
        if new_voice_calls > 0:
            print(f"✅ Voice calls were triggered!")
            
            # Show the new voice calls
            recent_calls = db.query(VoiceCall).filter(
                VoiceCall.email_log_id == test_email.id
            ).all()
            
            for call in recent_calls:
                print(f"  📞 {call.employee_name} ({call.recipient})")
                print(f"     Status: {call.status}")
                print(f"     Call ID: {call.vapi_call_id}")
                if call.error_message:
                    print(f"     Error: {call.error_message}")
        else:
            print(f"❌ No voice calls were triggered!")
            print(f"   This means voice calling is not working in the email flow")
        
        # Check the email status
        updated_email = db.query(EmailLog).filter(EmailLog.id == test_email.id).first()
        print(f"\n📧 Email status after processing: {updated_email.status}")
        print(f"📝 WhatsApp summary: {updated_email.whatsapp_summary[:100] if updated_email.whatsapp_summary else 'None'}...")
        
        db.close()
        return new_voice_calls > 0
        
    except Exception as e:
        print(f"❌ Error in email processing test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_voice_calling_in_config():
    """Check voice calling configuration"""
    print("🔍 Checking Voice Calling Configuration")
    print("=" * 50)
    
    try:
        from start_monitoring_improved import get_config
        
        config = get_config()
        
        print(f"📋 Configuration values:")
        print(f"  • VOICE_CALLING_ENABLED: {config.get('voice_calling_enabled')}")
        print(f"  • VOICE_PROVIDER: {config.get('voice_provider')}")
        print(f"  • TWILIO_ACCOUNT_SID: {config.get('twilio_account_sid', 'Not set')[:10]}..." if config.get('twilio_account_sid') else "  • TWILIO_ACCOUNT_SID: Not set")
        print(f"  • TWILIO_AUTH_TOKEN: {config.get('twilio_auth_token', 'Not set')[:10]}..." if config.get('twilio_auth_token') else "  • TWILIO_AUTH_TOKEN: Not set")
        print(f"  • TWILIO_FROM_NUMBER: {config.get('twilio_from_number', 'Not set')}")
        
        # Check if all required values are present
        required_for_voice = [
            'voice_calling_enabled',
            'twilio_account_sid', 
            'twilio_auth_token',
            'twilio_from_number'
        ]
        
        missing = []
        for key in required_for_voice:
            if not config.get(key):
                missing.append(key)
        
        if missing:
            print(f"\n❌ Missing configuration: {', '.join(missing)}")
            return False
        else:
            print(f"\n✅ All voice calling configuration present")
            return True
            
    except Exception as e:
        print(f"❌ Error checking configuration: {str(e)}")
        return False

def test_notification_service_directly():
    """Test notification service directly"""
    print(f"\n🔔 Testing Notification Service Directly")
    print("=" * 50)
    
    try:
        from app.services.notification_service import get_notification_service
        from app.models import EmailLog, get_db
        from start_monitoring_improved import get_config
        from datetime import datetime
        
        config = get_config()
        notification_service = get_notification_service(config)
        
        print(f"📞 Twilio caller: {notification_service.twilio_caller}")
        print(f"📞 Caller type: {type(notification_service.twilio_caller).__name__}")
        
        if not notification_service.twilio_caller:
            print(f"❌ Twilio caller is None - voice calls will not work")
            return False
        
        # Create test email
        db = get_db()
        test_email = EmailLog(
            message_id=f"direct_test_{int(datetime.now().timestamp())}",
            sender="Test Sender <<EMAIL>>",
            recipient="<EMAIL>",
            subject="Direct Notification Test",
            status="processed",
            whatsapp_summary="This is a direct test of the notification service."
        )
        
        db.add(test_email)
        db.commit()
        
        print(f"📧 Created test email: {test_email.id}")
        
        # Send notifications
        results = notification_service.send_notifications(test_email, db)
        
        print(f"📊 Results:")
        print(f"  • WhatsApp: {len(results.get('whatsapp_notifications', []))}")
        print(f"  • SMS: {len(results.get('sms_notifications', []))}")
        print(f"  • Voice: {len(results.get('voice_calls', []))}")
        
        voice_calls = results.get('voice_calls', [])
        if voice_calls:
            print(f"✅ Voice calls initiated!")
            for call in voice_calls:
                print(f"  📞 {call.get('employee_name')} ({call.get('recipient')}): {call.get('status')}")
        else:
            print(f"❌ No voice calls initiated")
        
        db.close()
        return len(voice_calls) > 0
        
    except Exception as e:
        print(f"❌ Error in direct notification test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 Voice Calling Email Flow Test")
    print("=" * 70)
    
    # Step 1: Check configuration
    config_ok = check_voice_calling_in_config()
    
    # Step 2: Test notification service directly
    if config_ok:
        direct_test_ok = test_notification_service_directly()
    else:
        direct_test_ok = False
    
    # Step 3: Test complete email processing flow
    if config_ok:
        email_flow_ok = test_email_processing_with_voice()
    else:
        email_flow_ok = False
    
    print(f"\n🎯 Test Summary")
    print("=" * 50)
    print(f"✅ Configuration: {'OK' if config_ok else 'Failed'}")
    print(f"✅ Direct Notification: {'OK' if direct_test_ok else 'Failed'}")
    print(f"✅ Email Flow: {'OK' if email_flow_ok else 'Failed'}")
    
    if email_flow_ok:
        print(f"\n🎉 Voice calling is working in the email flow!")
        print(f"📱 Employees should receive voice calls when emails are processed")
    elif direct_test_ok:
        print(f"\n⚠️ Voice calling works directly but not in email flow")
        print(f"💡 There might be an issue in the email processing pipeline")
    else:
        print(f"\n❌ Voice calling is not working")
        print(f"💡 Check Twilio configuration and credentials")

if __name__ == "__main__":
    main()
