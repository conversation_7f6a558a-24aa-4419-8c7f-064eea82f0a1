# Core FastAPI and web framework dependencies
fastapi==0.115.12
uvicorn==0.34.2
starlette==0.46.2
pydantic==2.11.4
pydantic_core==2.33.2

# Database (PostgreSQL)
SQLAlchemy==2.0.41
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Environment and configuration
python-dotenv==1.1.0

# Email processing
imaplib2==3.6
email_validator==2.2.0

# HTTP requests for Meta WhatsApp API and Twilio API
requests==2.32.3
httpx==0.28.1

# Twilio SMS and Voice API
twilio==9.6.1

# AI/OpenAI
openai==1.82.0

# Testing
pytest==8.3.5
pytest-mock==3.14.0

# Utilities
python-dateutil==2.9.0.post0
typing_extensions==4.13.2

# Twilio is now used for SMS and Voice calling

# Optional: Keep these if you need them for other features
# Flask==3.1.1  # Remove if not using Flask
# Werkzeug==3.1.3  # Remove if not using Flask

# Data processing (if needed)
pandas==2.2.3
numpy==2.2.6

# Logging and monitoring
# supervisor==4.2.1  # For process management in production

# Security
cryptography==45.0.2
PyJWT==2.10.1

# Additional utilities that might be useful
python-bidi==0.6.6
PyYAML==6.0.2
click==8.1.8
Jinja2==3.1.6
MarkupSafe==3.0.2

# HTTP and networking
aiohttp==3.11.18
aiosignal==1.3.2
anyio==4.9.0
certifi==2025.4.26
charset-normalizer==3.4.2
idna==3.10
urllib3==2.4.0

# Core Python utilities
six==1.17.0
packaging==25.0
setuptools
wheel
