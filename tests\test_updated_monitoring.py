#!/usr/bin/env python3
"""
Test the updated monitoring script to ensure SMS notifications are included
"""

import sys
import os
import time
sys.path.append('.')

from app.models import EmailLog, get_db
from start_monitoring_improved import get_config, monitor_emails_improved

def create_test_email():
    """Create a test email in the database to simulate incoming email"""
    print("📧 Creating test email to simulate incoming email...")
    
    db = get_db()
    
    # Create a test email that looks like it came from the allowed sender
    config = get_config()
    allowed_sender = config.get('allowed_sender_email', '<EMAIL>')
    
    test_email = EmailLog(
        message_id=f"test-monitoring-{int(time.time())}",
        sender=allowed_sender,
        recipient="<EMAIL>",
        subject="Test Email for Updated Monitoring Script",
        status="received"
    )
    
    db.add(test_email)
    db.commit()
    
    print(f"✅ Created test email with ID: {test_email.id}")
    print(f"   📧 From: {test_email.sender}")
    print(f"   📧 Subject: {test_email.subject}")
    return test_email, db

def test_config():
    """Test the configuration to ensure SMS settings are loaded"""
    print("\n🔧 Testing configuration...")
    
    config = get_config()
    
    print(f"   📱 WhatsApp enabled: {not config.get('mock_whatsapp', True)}")
    print(f"   📨 SMS enabled: {config.get('sms_enabled', False)}")
    print(f"   📞 Voice calling enabled: {config.get('voice_calling_enabled', False)}")
    print(f"   🔑 Twilio Account SID: {'✅' if config.get('twilio_account_sid') else '❌'}")
    print(f"   🔑 Twilio Auth Token: {'✅' if config.get('twilio_auth_token') else '❌'}")
    print(f"   📱 SMS from number: {config.get('twilio_sms_from_number', 'Not set')}")
    print(f"   🌐 API base URL: {config.get('api_base_url', 'Not set')}")
    
    return config

def test_monitoring_script():
    """Test the updated monitoring script"""
    print("\n🚀 Testing updated monitoring script...")
    
    try:
        config = get_config()
        db = get_db()
        
        # Run the monitoring function
        result = monitor_emails_improved(config, db)
        
        print(f"\n📊 Monitoring Results:")
        print(f"   ✅ Success: {result.get('success', False)}")
        print(f"   📧 Total checked: {result.get('total_checked', 0)}")
        print(f"   📧 New emails: {result.get('new_emails', 0)}")
        print(f"   📧 Processed emails: {result.get('processed_emails', 0)}")
        print(f"   📱 Notifications sent: {result.get('notifications_sent', 0)}")
        print(f"   📤 Replies sent: {result.get('replies_sent', 0)}")
        
        if result.get('error'):
            print(f"   ❌ Error: {result['error']}")
        
        return result
        
    except Exception as e:
        print(f"❌ Monitoring test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

def check_notification_records():
    """Check what notification records were created"""
    print("\n🔍 Checking notification records in database...")
    
    try:
        from app.models import WhatsAppNotification, SMSNotification, VoiceCall
        db = get_db()
        
        # Get recent notifications (last 10 minutes)
        from datetime import datetime, timedelta
        recent_time = datetime.now() - timedelta(minutes=10)
        
        # Check WhatsApp notifications
        whatsapp_count = db.query(WhatsAppNotification).filter(
            WhatsAppNotification.sent_at >= recent_time
        ).count()
        print(f"   📱 Recent WhatsApp notifications: {whatsapp_count}")
        
        # Check SMS notifications
        sms_notifications = db.query(SMSNotification).filter(
            SMSNotification.sent_at >= recent_time
        ).all()
        print(f"   📨 Recent SMS notifications: {len(sms_notifications)}")
        
        for sms in sms_notifications:
            status_icon = "✅" if sms.status == 'sent' else "❌"
            print(f"      {status_icon} {sms.employee_name} ({sms.recipient}) - {sms.status}")
            if sms.message_id:
                print(f"         Message ID: {sms.message_id}")
        
        # Check Voice calls
        voice_count = db.query(VoiceCall).filter(
            VoiceCall.initiated_at >= recent_time
        ).count()
        print(f"   📞 Recent voice calls: {voice_count}")
        
    except Exception as e:
        print(f"❌ Database check error: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Testing Updated Email Monitoring Script")
    print("=" * 60)
    
    # Step 1: Test configuration
    config = test_config()
    
    # Step 2: Test monitoring script
    result = test_monitoring_script()
    
    # Step 3: Check notification records
    check_notification_records()
    
    # Summary
    print(f"\n🎯 Test Summary:")
    print(f"   🔧 Configuration: {'✅' if config.get('sms_enabled') else '❌'}")
    print(f"   🚀 Monitoring script: {'✅' if result.get('success') else '❌'}")
    
    if result.get('success'):
        notifications_sent = result.get('notifications_sent', 0)
        if notifications_sent > 0:
            print(f"   📱 Notifications: ✅ ({notifications_sent} sent)")
            print(f"\n🎉 SUCCESS: Updated monitoring script is working!")
            print(f"   📱 WhatsApp notifications: Working")
            print(f"   📨 SMS notifications: Working")
            print(f"   📞 Voice calls: {'Working' if config.get('voice_calling_enabled') else 'Disabled'}")
        else:
            print(f"   📱 Notifications: ⚠️ (None sent - check if emails match criteria)")
    else:
        print(f"   🚀 Monitoring script: ❌ (Check logs for errors)")
    
    print(f"\n💡 Next Steps:")
    print(f"   • Send a real email from {config.get('allowed_sender_email')} to trigger the system")
    print(f"   • Check your phones for both WhatsApp and SMS messages")
    print(f"   • Monitor the console output for detailed notification logs")

if __name__ == "__main__":
    main()
