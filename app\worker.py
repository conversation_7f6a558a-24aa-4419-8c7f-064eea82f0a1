import os
from dotenv import load_dotenv
from fastapi import FastAPI, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import logging
import time
from typing import Dict, Optional

from .models import get_db
from .utils.logging_utils import setup_logging, RequestLoggingMiddleware, Error<PERSON>andler
from .services.email_monitor import EmailMonitor, EmailProcessor, monitor_emails
from .services.ai_summarizer import get_ai_summarizer
from .services.whatsapp_notifier import get_whatsapp_notifier
from .services.email_replier import get_email_replier
from .services.notification_service import get_notification_service
from .services.action_monitor_service import get_action_monitor_service
from .services.voice_call_analytics import get_voice_call_analytics_service

# Load environment variables
load_dotenv()

# Set up logging
logger = setup_logging("email_monitor", "logs")
error_handler = ErrorHandler(logger)

# Create FastAPI app
app = FastAPI(
    title="Email Monitor Agent",
    description="AI-powered email monitoring, summarization, and notification system",
    version="1.0.0"
)

# Add middleware
app.add_middleware(RequestLoggingMiddleware, logger=logger)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration
def get_config():
    """Get application configuration from environment variables"""
    return {
        # Email monitoring (IMAP)
        "imap_host": os.getenv("IMAP_HOST"),
        "imap_port": int(os.getenv("IMAP_PORT", "993")),
        "imap_username": os.getenv("IMAP_USERNAME"),
        "imap_password": os.getenv("IMAP_PASSWORD"),
        "use_ssl": os.getenv("IMAP_USE_SSL", "True").lower() == "true",
        "mailbox": os.getenv("IMAP_MAILBOX", "INBOX"),
        "allowed_sender_email": os.getenv("ALLOWED_SENDER_EMAIL"),

        # AI summarization
        "use_openai": os.getenv("USE_OPENAI", "True").lower() == "true",
        "openai_api_key": os.getenv("OPENAI_API_KEY"),
        "openai_model": os.getenv("OPENAI_MODEL", "gpt-4"),
        "local_model_path": os.getenv("LOCAL_MODEL_PATH", "models/llama-7b"),

        # WhatsApp notification (Meta Cloud API)
        "mock_whatsapp": os.getenv("MOCK_WHATSAPP", "False").lower() == "true",
        "meta_api_token": os.getenv("META_API_TOKEN"),
        "meta_phone_number_id": os.getenv("META_PHONE_NUMBER_ID"),
        "team_numbers": os.getenv("TEAM_NUMBERS", "").split(","),

        # Email reply (SMTP)
        "smtp_host": os.getenv("SMTP_HOST"),
        "smtp_port": int(os.getenv("SMTP_PORT", "587")),
        "smtp_username": os.getenv("SMTP_USERNAME"),
        "smtp_password": os.getenv("SMTP_PASSWORD"),
        "smtp_use_ssl": os.getenv("SMTP_USE_SSL", "False").lower() == "true",
        "default_sender": os.getenv("DEFAULT_SENDER"),
        "mock_email": os.getenv("MOCK_EMAIL", "False").lower() == "true",

        # Voice calling with Twilio
        "voice_calling_enabled": os.getenv("VOICE_CALLING_ENABLED", "False").lower() == "true",
        "voice_provider": os.getenv("VOICE_PROVIDER", "twilio"),
        "twilio_account_sid": os.getenv("TWILIO_ACCOUNT_SID"),
        "twilio_auth_token": os.getenv("TWILIO_AUTH_TOKEN"),
        "twilio_from_number": os.getenv("TWILIO_FROM_NUMBER"),

        # SMS notifications with Twilio
        "sms_enabled": os.getenv("SMS_ENABLED", "True").lower() == "true",
        "sms_provider": os.getenv("SMS_PROVIDER", "twilio"),
        "twilio_sms_from_number": os.getenv("TWILIO_SMS_FROM_NUMBER", "EmailAgent"),

        # API configuration
        "api_base_url": os.getenv("API_BASE_URL", "http://localhost:8000"),

        # Polling interval
        "polling_interval": int(os.getenv("POLLING_INTERVAL", "300")),  # 5 minutes
    }

# Background task for email monitoring
def background_email_monitor(db: Session):
    """Background task for monitoring emails"""
    config = get_config()

    while True:
        try:
            logger.info("Starting email monitoring cycle")
            monitor_emails(config, db)
            logger.info(f"Email monitoring cycle completed, sleeping for {config['polling_interval']} seconds")
            time.sleep(config["polling_interval"])
        except Exception as e:
            error_handler.handle_error(e, "background_email_monitor")
            # Sleep before retrying to avoid rapid failure loops
            time.sleep(60)

# Process a single email through the entire pipeline
def process_email_pipeline(email_log_id: int, db: Session):
    """
    Process a single email through the entire pipeline:
    1. AI summarization
    2. WhatsApp notification
    3. SMS notification
    4. Voice calls (if enabled)
    5. Email reply
    """
    config = get_config()

    try:
        # Get email log
        from .models import EmailLog
        email_log = db.query(EmailLog).filter(EmailLog.id == email_log_id).first()

        if not email_log:
            logger.error(f"Email log {email_log_id} not found")
            return

        # Get email content
        # In a real implementation, this would retrieve the content from the database
        # or re-fetch from the email server if needed
        email_content = "Sample email content for testing"

        # AI summarization
        logger.info(f"Processing email {email_log_id} with AI")
        ai_summarizer = get_ai_summarizer(config)
        ai_result = ai_summarizer.process_email(email_log, email_content, db)

        if not ai_result.get('success', False):
            logger.error(f"AI processing failed for email {email_log_id}")
            return

        # Send notifications (WhatsApp + SMS + Voice calls)
        logger.info(f"Sending notifications (WhatsApp + SMS + Voice calls) for email {email_log_id}")
        notification_service = get_notification_service(config)
        notification_results = notification_service.send_notifications(email_log, db)

        # Email reply
        logger.info(f"Sending email reply for email {email_log_id}")
        email_replier = get_email_replier(config)
        reply = email_replier.send_reply(email_log, db)

        logger.info(f"Email {email_log_id} processed successfully")
    except Exception as e:
        error_handler.handle_error(e, f"process_email_pipeline for email {email_log_id}")

# API routes
@app.get("/api")
def read_api_root():
    """API root endpoint"""
    return {"message": "Email Monitor Agent API"}

@app.post("/api/monitor/start")
def start_monitoring(background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Start email monitoring in the background"""
    try:
        background_tasks.add_task(background_email_monitor, db)
        logger.info("Email monitoring started in background")
        return {"message": "Email monitoring started in background"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "start_monitoring")
        return {"error": str(e), "details": error_info}

@app.post("/api/emails/{email_id}/process")
def process_email(email_id: int, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Process a specific email through the pipeline"""
    try:
        background_tasks.add_task(process_email_pipeline, email_id, db)
        logger.info(f"Email {email_id} queued for processing")
        return {"message": f"Email {email_id} queued for processing"}
    except Exception as e:
        error_info = error_handler.handle_error(e, f"process_email {email_id}")
        return {"error": str(e), "details": error_info}

@app.post("/api/notifications/retry")
def retry_notifications(db: Session = Depends(get_db)):
    """Retry failed WhatsApp notifications"""
    try:
        config = get_config()
        whatsapp_notifier = get_whatsapp_notifier(config)
        success_count = whatsapp_notifier.retry_failed_notifications(max_retries=3, db_session=db)
        logger.info(f"Retried {success_count} WhatsApp notifications")
        return {"message": f"Retried {success_count} WhatsApp notifications"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "retry_notifications")
        return {"error": str(e), "details": error_info}

@app.post("/api/replies/retry")
def retry_replies(db: Session = Depends(get_db)):
    """Retry failed email replies"""
    try:
        config = get_config()
        email_replier = get_email_replier(config)
        success_count = email_replier.retry_failed_replies(db_session=db, max_retries=3)
        logger.info(f"Retried {success_count} email replies")
        return {"message": f"Retried {success_count} email replies"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "retry_replies")
        return {"error": str(e), "details": error_info}

@app.post("/api/calls/retry")
def retry_voice_calls(db: Session = Depends(get_db)):
    """Retry failed voice calls"""
    try:
        config = get_config()
        notification_service = get_notification_service(config)
        retry_results = notification_service.retry_failed_calls(db)
        logger.info(f"Voice call retry completed: {retry_results}")
        return {"message": "Voice call retry completed", "results": retry_results}
    except Exception as e:
        error_info = error_handler.handle_error(e, "retry_voice_calls")
        return {"error": str(e), "details": error_info}

# Background task for retrying failed voice calls
def background_call_retry(db: Session):
    """Background task for retrying failed voice calls every 10 minutes"""
    config = get_config()

    if not config.get('vapi_enabled', False):
        logger.info("VAPI not enabled, skipping call retry background task")
        return

    while True:
        try:
            logger.info("Starting voice call retry cycle")
            notification_service = get_notification_service(config)
            retry_results = notification_service.retry_failed_calls(db)
            logger.info(f"Voice call retry cycle completed: {retry_results}")

            # Sleep for 10 minutes before next retry cycle
            time.sleep(600)  # 10 minutes
        except Exception as e:
            error_handler.handle_error(e, "background_call_retry")
            # Sleep before retrying to avoid rapid failure loops
            time.sleep(300)  # 5 minutes on error

@app.post("/api/calls/retry/start")
def start_call_retry_background(background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Start voice call retry monitoring in the background"""
    try:
        background_tasks.add_task(background_call_retry, db)
        logger.info("Voice call retry monitoring started in background")
        return {"message": "Voice call retry monitoring started in background"}
    except Exception as e:
        error_info = error_handler.handle_error(e, "start_call_retry_background")
        return {"error": str(e), "details": error_info}

# Action Monitoring API endpoints
@app.get("/api/actions")
def get_actions(
    action_type: Optional[str] = None,
    action_category: Optional[str] = None,
    status: Optional[str] = None,
    email_log_id: Optional[int] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get action monitoring records with optional filtering"""
    try:
        action_monitor = get_action_monitor_service(db)
        actions = action_monitor.get_actions(
            action_type=action_type,
            action_category=action_category,
            status=status,
            email_log_id=email_log_id,
            limit=limit,
            offset=offset
        )
        return [action.to_dict() for action in actions]
    except Exception as e:
        error_info = error_handler.handle_error(e, "get_actions")
        return {"error": str(e), "details": error_info}

@app.get("/api/actions/statistics")
def get_action_statistics(hours: int = 24, db: Session = Depends(get_db)):
    """Get action statistics for the specified time period"""
    try:
        action_monitor = get_action_monitor_service(db)
        stats = action_monitor.get_action_statistics(hours=hours)
        return stats
    except Exception as e:
        error_info = error_handler.handle_error(e, "get_action_statistics")
        return {"error": str(e), "details": error_info}

@app.get("/api/actions/{action_id}")
def get_action(action_id: int, db: Session = Depends(get_db)):
    """Get a specific action by ID"""
    try:
        from .models import ActionMonitor
        action = db.query(ActionMonitor).filter(ActionMonitor.id == action_id).first()
        if not action:
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail=f"Action {action_id} not found")
        return action.to_dict()
    except Exception as e:
        error_info = error_handler.handle_error(e, f"get_action {action_id}")
        return {"error": str(e), "details": error_info}

# Employee Management (add directly to worker app)
from pydantic import BaseModel
from typing import List, Optional

class EmployeeCreate(BaseModel):
    name: str
    phone: str

class EmployeeUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None

class EmployeeResponse(BaseModel):
    id: int
    name: str
    phone: str
    status: str = "active"
    created_at: str

# Import shared employee service
from .services.employee_service import get_employee_service

@app.get("/api/employees", response_model=List[EmployeeResponse])
def get_employees():
    """Get all employees"""
    employee_service = get_employee_service()
    return employee_service.get_all_employees()

@app.post("/api/employees", response_model=EmployeeResponse)
def create_employee(employee: EmployeeCreate):
    """Create a new employee"""
    employee_service = get_employee_service()
    return employee_service.create_employee(employee.name, employee.phone)

@app.get("/api/employees/{employee_id}", response_model=EmployeeResponse)
def get_employee(employee_id: int):
    """Get a specific employee by ID"""
    employee_service = get_employee_service()
    employee = employee_service.get_employee(employee_id)
    if not employee:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Employee not found")
    return employee

@app.put("/api/employees/{employee_id}", response_model=EmployeeResponse)
def update_employee(employee_id: int, employee: EmployeeUpdate):
    """Update an employee"""
    employee_service = get_employee_service()
    updated_employee = employee_service.update_employee(
        employee_id,
        name=employee.name,
        phone=employee.phone
    )
    if not updated_employee:
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Employee not found")
    return updated_employee

@app.delete("/api/employees/{employee_id}")
def delete_employee(employee_id: int):
    """Delete an employee"""
    employee_service = get_employee_service()
    if not employee_service.delete_employee(employee_id):
        from fastapi import HTTPException
        raise HTTPException(status_code=404, detail="Employee not found")
    return {"message": "Employee deleted successfully"}

# Voice Call Analytics API endpoints
@app.get("/api/voice-calls/analytics")
def get_voice_call_analytics(hours: int = 24, db: Session = Depends(get_db)):
    """Get comprehensive voice call analytics"""
    try:
        analytics_service = get_voice_call_analytics_service(db)
        analytics = analytics_service.get_call_analytics(hours=hours)
        return analytics
    except Exception as e:
        error_info = error_handler.handle_error(e, "get_voice_call_analytics")
        return {"error": str(e), "details": error_info}

@app.get("/api/voice-calls/employee-stats")
def get_employee_call_statistics(
    employee_id: Optional[int] = None,
    hours: int = 24,
    db: Session = Depends(get_db)
):
    """Get call statistics by employee"""
    try:
        analytics_service = get_voice_call_analytics_service(db)
        stats = analytics_service.get_employee_call_stats(employee_id=employee_id, hours=hours)
        return stats
    except Exception as e:
        error_info = error_handler.handle_error(e, "get_employee_call_statistics")
        return {"error": str(e), "details": error_info}

@app.get("/api/voice-calls/logs")
def get_voice_call_logs(
    email_log_id: Optional[int] = None,
    employee_id: Optional[int] = None,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """Get voice call logs with filtering"""
    try:
        analytics_service = get_voice_call_analytics_service(db)
        logs = analytics_service.get_call_logs(
            email_log_id=email_log_id,
            employee_id=employee_id,
            status=status,
            limit=limit,
            offset=offset
        )
        return [log.to_dict() for log in logs]
    except Exception as e:
        error_info = error_handler.handle_error(e, "get_voice_call_logs")
        return {"error": str(e), "details": error_info}

@app.get("/api/voice-calls/{call_id}")
def get_voice_call_details(call_id: int, db: Session = Depends(get_db)):
    """Get detailed information for a specific voice call"""
    try:
        from .models import VoiceCallLog
        call_log = db.query(VoiceCallLog).filter(VoiceCallLog.id == call_id).first()
        if not call_log:
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail=f"Voice call {call_id} not found")
        return call_log.to_dict()
    except Exception as e:
        error_info = error_handler.handle_error(e, f"get_voice_call_details {call_id}")
        return {"error": str(e), "details": error_info}

@app.post("/api/voice-calls/{call_id}/update-status")
def update_voice_call_status(
    call_id: int,
    request: dict,
    db: Session = Depends(get_db)
):
    """Update voice call status and metrics"""
    try:
        analytics_service = get_voice_call_analytics_service(db)

        status = request.get('status')
        external_call_id = request.get('external_call_id') or request.get('vapi_call_id')  # Support both

        # Extract additional fields from request
        update_fields = {}
        for field in ['cost_per_minute', 'total_cost', 'duration_seconds', 'audio_quality',
                     'call_transcript', 'error_message', 'failure_reason']:
            if field in request:
                update_fields[field] = request[field]

        success = analytics_service.update_call_status(
            call_id=call_id,
            status=status,
            external_call_id=external_call_id,
            **update_fields
        )

        if success:
            return {"message": f"Voice call {call_id} updated successfully"}
        else:
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail=f"Voice call {call_id} not found")

    except Exception as e:
        error_info = error_handler.handle_error(e, f"update_voice_call_status {call_id}")
        return {"error": str(e), "details": error_info}

# Email API endpoints (add directly to worker app)
from .models import EmailLog, WhatsAppNotification, EmailReply
from .schemas import EmailLogResponse, EmailLogDetail
from sqlalchemy import func

@app.get("/api/emails", response_model=List[EmailLogResponse])
def get_emails(
    status: Optional[str] = None,
    sender: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all processed emails with optional filtering"""
    try:
        query = db.query(EmailLog)

        # Apply filters
        if status:
            query = query.filter(EmailLog.status == status)
        if sender:
            query = query.filter(EmailLog.sender.contains(sender))

        # Apply pagination and ordering
        emails = query.order_by(EmailLog.received_at.desc()).offset(skip).limit(limit).all()

        return emails
    except Exception as e:
        logger.error(f"Error getting emails: {str(e)}")
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/emails/{email_id}", response_model=EmailLogDetail)
def get_email(email_id: int, db: Session = Depends(get_db)):
    """Get a specific email by ID"""
    try:
        email = db.query(EmailLog).filter(EmailLog.id == email_id).first()

        if not email:
            from fastapi import HTTPException
            raise HTTPException(status_code=404, detail=f"Email with ID {email_id} not found")

        return email
    except Exception as e:
        logger.error(f"Error getting email {email_id}: {str(e)}")
        from fastapi import HTTPException
        raise HTTPException(status_code=500, detail=str(e))

# Import other API routes (for any remaining endpoints)
from .main import app as api_app
# Don't mount the entire app, just use it for reference

# Frontend serving
import pathlib
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

# Get the current directory
current_dir = pathlib.Path(__file__).parent.parent

# Serve frontend static files
try:
    frontend_dir = current_dir / "frontend"
    if frontend_dir.exists():
        app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")
        logger.info(f"✅ Mounted static files from: {frontend_dir}")
    else:
        logger.warning(f"❌ Frontend directory not found: {frontend_dir}")
except Exception as e:
    logger.error(f"❌ Error mounting static files: {e}")

@app.get("/")
def serve_root():
    """Serve the main dashboard at root"""
    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        logger.error(f"Frontend file not found: {frontend_file}")
        return {"error": "Frontend not found", "message": "Dashboard files are missing"}

@app.get("/dashboard")
def serve_dashboard():
    """Serve the dashboard frontend"""
    # Try the new simplified dashboard first
    dashboard_file = current_dir / "frontend" / "dashboard.html"
    if dashboard_file.exists():
        return FileResponse(str(dashboard_file))

    # Fallback to original
    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        logger.error(f"Frontend file not found: {frontend_file}")
        return {"error": "Frontend not found", "message": "Dashboard files are missing"}

@app.get("/frontend")
def serve_frontend():
    """Serve the frontend"""
    dashboard_file = current_dir / "frontend" / "dashboard.html"
    if dashboard_file.exists():
        return FileResponse(str(dashboard_file))

    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        logger.error(f"Frontend file not found: {frontend_file}")
        return {"error": "Frontend not found", "message": "Dashboard files are missing"}

# Serve individual static files as fallback
@app.get("/css/{file_path:path}")
def serve_css(file_path: str):
    """Serve CSS files"""
    from fastapi import HTTPException
    css_file = current_dir / "frontend" / "css" / file_path
    if css_file.exists():
        return FileResponse(str(css_file))
    else:
        logger.error(f"CSS file not found: {css_file}")
        raise HTTPException(status_code=404, detail=f"CSS file not found: {css_file}")

@app.get("/js/{file_path:path}")
def serve_js(file_path: str):
    """Serve JavaScript files"""
    from fastapi import HTTPException
    js_file = current_dir / "frontend" / "js" / file_path
    if js_file.exists():
        return FileResponse(str(js_file))
    else:
        logger.error(f"JS file not found: {js_file}")
        raise HTTPException(status_code=404, detail=f"JS file not found: {js_file}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.worker:app", host="0.0.0.0", port=8080, reload=True)
