"""
Twilio Voice API integration for making voice calls to employees.
"""

import logging
from typing import Dict
from datetime import datetime
from ..models import <PERSON>ailLog
from twilio.rest import Client
from twilio.base.exceptions import TwilioException
from twilio.twiml.voice_response import VoiceResponse

logger = logging.getLogger(__name__)


class TwilioCaller:
    """
    Twilio Voice API integration for making voice calls to employees.
    """
    
    def __init__(self, 
                 account_sid: str,
                 auth_token: str,
                 from_number: str = None):
        """
        Initialize the Twilio caller.
        
        Args:
            account_sid: Twilio Account SID
            auth_token: Twilio Auth Token
            from_number: Phone number to call from
        """
        self.account_sid = account_sid
        self.auth_token = auth_token
        self.from_number = from_number or "Unknown"
        self.client = Client(account_sid, auth_token)
        
    def make_call(self, 
                  recipient_phone: str, 
                  email_log: EmailLog, 
                  employee_name: str = None) -> Dict:
        """
        Make a voice call to an employee using Twilio Voice API.
        
        Args:
            recipient_phone: Phone number to call (with country code)
            email_log: EmailLog instance containing email details
            employee_name: Name of the employee being called
            
        Returns:
            Dict: Call response with call_id and status
        """
        try:
            # Prepare call message
            call_message = self._format_call_message(email_log, employee_name)
            
            # Create TwiML for the call
            twiml = VoiceResponse()
            twiml.say(call_message, voice='alice', language='en-US')
            
            logger.info(f"Making Twilio voice call to {recipient_phone} for employee {employee_name}")
            
            # Make the call
            call = self.client.calls.create(
                twiml=str(twiml),
                to=recipient_phone,
                from_=self.from_number
            )
            
            logger.info(f"Twilio call initiated successfully: {call.sid}")
            return {
                'success': True,
                'call_id': call.sid,
                'status': call.status,
                'direction': call.direction,
                'to': call.to,
                'from': call.from_,
                'price': call.price,
                'price_unit': call.price_unit,
                'duration': call.duration
            }
                
        except TwilioException as e:
            error_msg = f"Twilio Voice API error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'error_code': getattr(e, 'code', None),
                'error_status': getattr(e, 'status', None)
            }
        except Exception as e:
            error_msg = f"Unexpected call error: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    def _format_call_message(self, email_log: EmailLog, employee_name: str = None) -> str:
        """
        Format the voice message for the call.
        
        Args:
            email_log: EmailLog instance
            employee_name: Employee name for personalization
            
        Returns:
            str: Formatted voice message
        """
        # Extract sender name (remove email if present)
        sender_name = email_log.sender.split('<')[0].strip() if '<' in email_log.sender else email_log.sender
        
        # Create personalized message
        greeting = f"Hello {employee_name}, " if employee_name else "Hello, "
        
        message = (
            f"{greeting}this is an automated notification from the Email Monitor Agent. "
            f"You have received an important email from {sender_name}. "
        )
        
        if email_log.subject:
            message += f"The subject is: {email_log.subject}. "
        
        message += (
            "Please check your email as soon as possible. "
            "Thank you."
        )
        
        return message

    def get_call_status(self, call_id: str) -> Dict:
        """
        Get the status of a specific call.
        
        Args:
            call_id: Twilio call SID
            
        Returns:
            Dict: Call status information
        """
        try:
            call = self.client.calls(call_id).fetch()
            return {
                'success': True,
                'call_id': call.sid,
                'status': call.status,
                'duration': call.duration,
                'price': call.price,
                'price_unit': call.price_unit,
                'start_time': call.start_time,
                'end_time': call.end_time
            }
        except TwilioException as e:
            error_msg = f"Error fetching call status: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    def hangup_call(self, call_id: str) -> Dict:
        """
        Hangup an active call.
        
        Args:
            call_id: Twilio call SID
            
        Returns:
            Dict: Hangup result
        """
        try:
            call = self.client.calls(call_id).update(status='completed')
            logger.info(f"Call {call_id} hung up successfully")
            return {
                'success': True,
                'call_id': call.sid,
                'status': call.status
            }
        except TwilioException as e:
            error_msg = f"Error hanging up call: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}


class MockVoiceService:
    """
    Mock voice service for testing and development.
    """
    
    def __init__(self, *args, **kwargs):
        logger.info("Using Mock Voice Service")
        
    def make_call(self, recipient_phone: str, email_log: EmailLog, employee_name: str = None) -> Dict:
        """Mock voice call"""
        logger.info(f"MOCK CALL to {recipient_phone} ({employee_name})")
        return {
            'success': True,
            'call_id': f'mock_call_{datetime.now().timestamp()}',
            'status': 'initiated'
        }
        
    def get_call_status(self, call_id: str) -> Dict:
        """Mock call status"""
        return {
            'success': True,
            'call_id': call_id,
            'status': 'completed',
            'duration': 30
        }
        
    def hangup_call(self, call_id: str) -> Dict:
        """Mock hangup"""
        return {
            'success': True,
            'call_id': call_id,
            'status': 'completed'
        }


def get_twilio_caller(config: Dict) -> TwilioCaller:
    """
    Factory function to get the Twilio caller based on configuration.
    
    Args:
        config: Configuration dictionary containing:
            - twilio_account_sid: Twilio Account SID
            - twilio_auth_token: Twilio Auth Token
            - twilio_from_number: From phone number
        
    Returns:
        TwilioCaller: Twilio caller instance
    """
    if not config.get('voice_calling_enabled', False):
        return MockVoiceService()
        
    return TwilioCaller(
        account_sid=config['twilio_account_sid'],
        auth_token=config['twilio_auth_token'],
        from_number=config.get('twilio_from_number')
    )
