#!/usr/bin/env python3
"""
Simple SMS test to check what's happening
"""

import os
from dotenv import load_dotenv
load_dotenv()

def test_twilio_basic():
    """Basic Twilio test"""
    print("🔍 Basic Twilio Test")
    print("=" * 30)
    
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    from_number = os.getenv('TWILIO_SMS_FROM_NUMBER')
    
    print(f"Account SID: {account_sid[:10]}..." if account_sid else "❌ Missing")
    print(f"Auth Token: {auth_token[:10]}..." if auth_token else "❌ Missing")
    print(f"From Number: {from_number}")
    
    if not all([account_sid, auth_token, from_number]):
        print("❌ Missing credentials")
        return False
    
    try:
        from twilio.rest import Client
        client = Client(account_sid, auth_token)
        
        # Check account
        account = client.api.accounts(account_sid).fetch()
        print(f"✅ Account Status: {account.status}")
        
        # Check balance
        balance = client.balance.fetch()
        print(f"💰 Balance: {balance.balance} {balance.currency}")
        
        return client
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def check_verified_numbers(client):
    """Check verified numbers"""
    print("\n📞 Verified Numbers:")
    print("=" * 30)
    
    try:
        verified = client.outgoing_caller_ids.list()
        if verified:
            for caller in verified:
                print(f"✅ {caller.phone_number}")
        else:
            print("❌ No verified numbers found!")
            print("   Go to: console.twilio.com > Phone Numbers > Verified Caller IDs")
            print("   Add these numbers:")
            print("   - +************ (Vishnu)")
            print("   - +************ (John)")
            print("   - +************ (karthik)")
            print("   - +************ (nirmal)")
        
        return verified
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return []

def send_simple_test(client):
    """Send a simple test SMS"""
    print("\n📱 Sending Simple Test SMS:")
    print("=" * 30)
    
    from_number = os.getenv('TWILIO_SMS_FROM_NUMBER')
    
    # Try sending to a known working number (your own for testing)
    test_number = "+************"  # John's number
    
    try:
        message = client.messages.create(
            body="🧪 Simple test from Email Monitor. If you receive this, SMS is working!",
            from_=from_number,
            to=test_number
        )
        
        print(f"✅ Message sent!")
        print(f"📊 SID: {message.sid}")
        print(f"📊 Status: {message.status}")
        print(f"📞 To: {message.to}")
        
        return message
        
    except Exception as e:
        print(f"❌ Error sending: {str(e)}")
        return None

def main():
    """Main function"""
    print("🚀 Simple SMS Test")
    print("=" * 50)
    
    # Test 1: Basic connection
    client = test_twilio_basic()
    
    if not client:
        print("\n❌ Cannot connect to Twilio")
        return
    
    # Test 2: Check verified numbers
    verified = check_verified_numbers(client)
    
    # Test 3: Send simple test
    if verified:
        message = send_simple_test(client)
        
        if message:
            print(f"\n✅ Test SMS sent successfully!")
            print(f"📱 Check phone +************ for the message")
        else:
            print(f"\n❌ Test SMS failed")
    else:
        print(f"\n⚠️ No verified numbers - cannot send SMS")
        print(f"   This is why SMS is not working!")

if __name__ == "__main__":
    main()
