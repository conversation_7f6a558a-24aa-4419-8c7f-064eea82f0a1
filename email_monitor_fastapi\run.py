#!/usr/bin/env python3
"""
Email Monitor Agent - Startup Script

This script starts the FastAPI application with email monitoring.
"""

import uvicorn
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

if __name__ == "__main__":
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8080"))
    reload = os.getenv("RELOAD", "True").lower() == "true"
    
    print("🚀 Starting Email Monitor Agent...")
    print(f"📧 Server will be available at: http://{host}:{port}")
    print(f"📚 API Documentation: http://{host}:{port}/docs")
    print(f"🔄 Auto-reload: {reload}")
    
    # Start the FastAPI application
    uvicorn.run(
        "app.worker:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )
