# Twilio SMS and Voice API Integration Requirements
# Install with: pip install -r requirements-twilio.txt

# Core Twilio API
twilio==9.6.1

# HTTP requests (required for Twilio API calls)
requests>=2.25.0
httpx>=0.24.0

# Additional networking utilities
urllib3>=1.26.0
certifi>=2021.0.0

# Date and time utilities for call scheduling
python-dateutil>=2.8.0

# Environment configuration
python-dotenv>=1.0.0

# JSON handling
simplejson>=3.17.0

# Logging utilities
colorlog>=6.0.0

# Optional: For advanced Twilio features
# PyJWT>=2.0.0  # For JWT tokens if needed
# cryptography>=3.0.0  # For secure communications

# Testing utilities
pytest>=7.0.0
pytest-mock>=3.10.0

# Development utilities
black>=22.0.0
flake8>=5.0.0
