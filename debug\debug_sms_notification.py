#!/usr/bin/env python3
"""
Debug SMS notification issue - check why SMS is not being sent
"""

import sys
import os
from dotenv import load_dotenv
sys.path.append('.')

# Load environment variables
load_dotenv()

from app.services.twilio_sms import get_sms_service
from app.services.notification_service import get_notification_service
from app.services.employee_service import get_employee_service
from app.models import EmailLog, get_db
from start_monitoring_improved import get_config
from datetime import datetime

def debug_sms_config():
    """Debug SMS configuration"""
    print("🔍 Debugging SMS Configuration")
    print("=" * 50)
    
    config = get_config()
    
    print("📋 SMS Configuration:")
    print(f"  • SMS Enabled: {config.get('sms_enabled', False)}")
    print(f"  • SMS Provider: {config.get('sms_provider', 'N/A')}")
    print(f"  • Twilio Account SID: {config.get('twilio_account_sid', 'N/A')[:8]}..." if config.get('twilio_account_sid') else "  • Twilio Account SID: Not set")
    print(f"  • Twilio Auth Token: {config.get('twilio_auth_token', 'N/A')[:8]}..." if config.get('twilio_auth_token') else "  • Twilio Auth Token: Not set")
    print(f"  • SMS From Number: {config.get('twilio_sms_from_number', 'N/A')}")
    
    return config

def debug_sms_service():
    """Debug SMS service initialization"""
    print("\n🔧 Debugging SMS Service Initialization")
    print("=" * 50)
    
    config = get_config()
    
    try:
        sms_service = get_sms_service(config)
        print(f"✅ SMS Service initialized: {type(sms_service).__name__}")
        
        if hasattr(sms_service, 'account_sid'):
            print(f"  • Account SID: {sms_service.account_sid[:8]}...")
            print(f"  • From Number: {sms_service.from_number}")
        else:
            print("  • Using Mock SMS Service (SMS disabled or credentials missing)")
            
        return sms_service
        
    except Exception as e:
        print(f"❌ Error initializing SMS service: {str(e)}")
        return None

def debug_employee_service():
    """Debug employee service"""
    print("\n👥 Debugging Employee Service")
    print("=" * 50)
    
    try:
        employee_service = get_employee_service()
        active_employees = employee_service.get_active_employees()
        
        print(f"✅ Found {len(active_employees)} active employees:")
        for emp in active_employees:
            print(f"  • {emp['name']}: {emp['phone']} (ID: {emp['id']})")
            
        return active_employees
        
    except Exception as e:
        print(f"❌ Error getting employees: {str(e)}")
        return []

def test_sms_service_directly():
    """Test SMS service directly"""
    print("\n📱 Testing SMS Service Directly")
    print("=" * 50)
    
    config = get_config()
    sms_service = get_sms_service(config)
    
    # Create a test email log
    db = get_db()
    test_email = EmailLog(
        message_id=f"debug_test_{int(datetime.now().timestamp())}",
        sender="Vishnu Bala Guru M <<EMAIL>>",
        recipient="<EMAIL>",
        subject="Debug SMS Test",
        status="processed",
        whatsapp_summary="This is a debug test to check SMS functionality. The system should send this message via SMS to active employees."
    )
    
    db.add(test_email)
    db.commit()
    
    print(f"📧 Created test email log: {test_email.id}")
    
    try:
        # Test SMS notifications
        sms_results = sms_service.send_notifications(test_email, db)
        
        print(f"📊 SMS Results: {len(sms_results)} notifications created")
        
        for result in sms_results:
            print(f"  • {result.employee_name} ({result.recipient}): {result.status}")
            if result.error_message:
                print(f"    Error: {result.error_message}")
            if result.message_id:
                print(f"    Message ID: {result.message_id}")
                
        return sms_results
        
    except Exception as e:
        print(f"❌ Error testing SMS service: {str(e)}")
        return []
    finally:
        db.close()

def test_notification_service():
    """Test the full notification service"""
    print("\n🔔 Testing Full Notification Service")
    print("=" * 50)
    
    config = get_config()
    notification_service = get_notification_service(config)
    
    # Create a test email log
    db = get_db()
    test_email = EmailLog(
        message_id=f"notification_test_{int(datetime.now().timestamp())}",
        sender="Vishnu Bala Guru M <<EMAIL>>",
        recipient="<EMAIL>",
        subject="Notification Service Test",
        status="processed",
        whatsapp_summary="This is a test of the full notification service. Both WhatsApp and SMS should be sent."
    )
    
    db.add(test_email)
    db.commit()
    
    print(f"📧 Created test email log: {test_email.id}")
    
    try:
        # Test full notification service
        results = notification_service.send_notifications(test_email, db)
        
        print(f"📊 Notification Results:")
        print(f"  • WhatsApp: {len(results.get('whatsapp_notifications', []))} sent")
        print(f"  • SMS: {len(results.get('sms_notifications', []))} sent")
        print(f"  • Voice: {len(results.get('voice_calls', []))} initiated")
        print(f"  • Success: {results.get('success_count', 0)}")
        print(f"  • Failed: {results.get('failure_count', 0)}")
        
        # Show details
        for whatsapp in results.get('whatsapp_notifications', []):
            print(f"    WhatsApp to {whatsapp.get('recipient')}: {whatsapp.get('status')}")
            
        for sms in results.get('sms_notifications', []):
            print(f"    SMS to {sms.get('recipient')}: {sms.get('status')}")
            if sms.get('error_message'):
                print(f"      Error: {sms.get('error_message')}")
                
        return results
        
    except Exception as e:
        print(f"❌ Error testing notification service: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}
    finally:
        db.close()

def main():
    """Main debug function"""
    print("🚀 SMS Notification Debug Tool")
    print("=" * 70)
    
    # Step 1: Check configuration
    config = debug_sms_config()
    
    # Step 2: Check SMS service
    sms_service = debug_sms_service()
    
    # Step 3: Check employees
    employees = debug_employee_service()
    
    # Step 4: Test SMS service directly
    if sms_service and employees:
        sms_results = test_sms_service_directly()
    
    # Step 5: Test full notification service
    notification_results = test_notification_service()
    
    print(f"\n🎯 Debug Summary")
    print("=" * 50)
    print(f"✅ Configuration: {'OK' if config.get('sms_enabled') else 'SMS Disabled'}")
    print(f"✅ SMS Service: {'OK' if sms_service else 'Failed'}")
    print(f"✅ Employees: {len(employees)} found")
    print(f"✅ Notification Service: {'OK' if notification_results else 'Failed'}")
    
    if not config.get('sms_enabled'):
        print(f"\n⚠️ SMS is disabled in configuration!")
        print(f"   Check SMS_ENABLED in your .env file")
    
    if not sms_service:
        print(f"\n⚠️ SMS service failed to initialize!")
        print(f"   Check Twilio credentials in your .env file")

if __name__ == "__main__":
    main()
