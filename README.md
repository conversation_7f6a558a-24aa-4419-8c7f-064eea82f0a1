# Email Monitor Agent - FastAPI

An AI-powered email monitoring and notification system built with FastAPI that automatically processes emails, generates summaries using AI, sends WhatsApp notifications via Meta Cloud API, makes voice calls via VAPI, and sends auto-replies with comprehensive action monitoring.

## 🚀 Features

### **Core Functionality**
- **Email Monitoring**: Monitors IMAP inbox for new emails with real-time processing
- **Sender Filtering**: Process emails only from specific sender addresses
- **AI Summarization**: Uses OpenAI GPT-4 or local LLM for intelligent email analysis
- **WhatsApp Notifications**: Sends notifications via Meta WhatsApp Cloud API
- **Voice Calls**: Automated voice calls to employees via VAPI integration
- **Auto-Reply**: Automatically responds to emails with AI-generated replies
- **Employee Management**: Full CRUD operations for managing team members

### **Advanced Features**
- **VAPI Voice Calling**: Automated voice notifications with retry logic
- **Action Monitoring**: Comprehensive tracking of all system activities
- **Real-time Dashboard**: Interactive web interface with live statistics
- **Smart Retry Logic**: Automatic retry for failed operations with 10-minute intervals
- **Cost Tracking**: Monitor expenses for voice calls and API usage
- **Performance Analytics**: Success rates, timing analysis, and bottleneck identification

### **Technical Features**
- **FastAPI REST API**: Full REST API for monitoring and management
- **Database Logging**: Tracks all emails, notifications, calls, and replies
- **Real-time Updates**: Auto-refreshing dashboard with live data
- **Responsive Design**: Mobile-friendly interface with modern UI/UX
- **Error Handling**: Comprehensive error logging and recovery mechanisms

## 📁 Project Structure

```
email_monitor_fastapi/
├── app/
│   ├── __init__.py
│   ├── models.py                    # Database models (EmailLog, VoiceCall, ActionMonitor)
│   ├── schemas.py                   # Pydantic schemas for API
│   ├── main.py                     # FastAPI application with API endpoints
│   ├── worker.py                   # Background worker and main application
│   ├── services/
│   │   ├── __init__.py
│   │   ├── email_monitor.py        # Email monitoring and IMAP handling
│   │   ├── ai_summarizer.py        # AI summarization (OpenAI/Local LLM)
│   │   ├── whatsapp_notifier.py    # WhatsApp notifications (Meta Cloud API)
│   │   ├── email_replier.py        # Email auto-reply functionality
│   │   ├── vapi_caller.py          # VAPI voice calling service
│   │   ├── notification_service.py # Unified notification service (WhatsApp + Voice)
│   │   └── action_monitor_service.py # Action monitoring and tracking
│   └── utils/
│       ├── __init__.py
│       └── logging_utils.py        # Logging configuration and utilities
├── frontend/                       # Web dashboard interface
│   ├── index.html                  # Main dashboard HTML
│   ├── css/
│   │   └── styles.css             # Dashboard styles
│   └── js/
│       ├── app.js                 # Main application logic
│       ├── dashboard.js           # Dashboard functionality
│       ├── emails.js              # Email management
│       ├── employees.js           # Employee management
│       ├── settings.js            # Settings management
│       └── monitoring.js          # Action monitoring dashboard
├── tests/
│   ├── __init__.py
│   ├── test_integration.py        # Integration tests
│   └── test_meta_api.py          # Meta WhatsApp API specific tests
├── .env.example                   # Environment variables template
├── requirements.txt               # Python dependencies
├── VAPI_INTEGRATION.md           # VAPI setup and usage guide
├── ACTION_MONITORING.md          # Action monitoring documentation
└── README.md                     # This file
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd email_monitor_fastapi
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual configuration
   ```

## ⚙️ Configuration

### Environment Variables

The system uses the `.env` file for configuration. Here are the current settings:

#### Database (PostgreSQL)
```env
DATABASE_URL=postgresql://postgres:admin@localhost:5432/Email_agent
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=Email_agent
POSTGRES_USER=postgres
POSTGRES_PASSWORD=admin
```

#### Email Monitoring (IMAP)
```env
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=igmnriwyhnbjcpbl
IMAP_USE_SSL=True
IMAP_MAILBOX=INBOX
ALLOWED_SENDER_EMAIL=<EMAIL>  # Filter by specific sender
```

#### AI Summarization
```env
USE_OPENAI=True
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4
```

#### WhatsApp Notifications (Meta Cloud API)
```env
MOCK_WHATSAPP=False
META_API_TOKEN=EAAkFRSVA9tgBOzTlvPrCS51ZCNNZCLFnVdHPbnJDzsZCitdNm7FouctCT12dq2N47MUt1sRgvkQZCuuyZAyboFdJ1EhZB0kGTZB7HpgzZAXPZAJGnnnOnHw9nyO07i5IXqpy4rfwmjKnj5D41xBLMvbWYxw8nrq7jYtg0gUJo4UAv7s5IXTDcoGnubbmJLbZCqHw9chgZDZ
META_PHONE_NUMBER_ID=***************
TEAM_NUMBERS=+************
```

#### SMS and Voice Calling with Twilio
```env
# SMS notifications
SMS_ENABLED=True
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_SMS_FROM_NUMBER=your_twilio_phone_number

# Voice calling
VOICE_CALLING_ENABLED=False
VOICE_PROVIDER=twilio
TWILIO_FROM_NUMBER=your_twilio_phone_number
```

#### Email Auto-Reply (SMTP)
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=igmnriwyhnbjcpbl
SMTP_USE_SSL=False
DEFAULT_SENDER=<EMAIL>
MOCK_EMAIL=False
```

#### API Configuration
```env
API_BASE_URL=http://localhost:8000
POLLING_INTERVAL=20  # Email check interval in seconds (currently set to 20 seconds)
```

### Meta WhatsApp Cloud API Setup

1. **Create Meta Developer Account**: Go to [developers.facebook.com](https://developers.facebook.com)
2. **Create WhatsApp Business App**: Create a new app and add WhatsApp product
3. **Get Access Token**: Generate a permanent access token
4. **Get Phone Number ID**: Note your WhatsApp Business phone number ID
5. **Add Webhook** (optional): For receiving message status updates

### Vonage Voice Calling Setup

The system uses Vonage Voice API for outbound calling to employees. To set up Vonage:

1. **Create Vonage Account**: Sign up at [vonage.com](https://vonage.com)
2. **Create Voice Application**: Set up a voice application in the Vonage dashboard
3. **Get API Credentials**:
   - API Key and Secret from your account dashboard
   - Application ID from your voice application
   - Private key for JWT authentication (optional)
   - Phone number for outbound calls
4. **Configure Environment**: Set up the Vonage environment variables in your .env file
5. **Enable Feature**: Set `VOICE_CALLING_ENABLED=True` to enable voice calling

The system will automatically call employees when WhatsApp notifications are sent, using the same scenario as before but with Vonage instead of VAPI.

## 🚀 Usage

### Running the Application

1. **Start the FastAPI server**:
   ```bash
   cd email_monitor_fastapi
   python -m app.worker
   ```

2. **Access the Application**:
   - **Web Dashboard**: http://localhost:8082 (Main interface)
   - **API Documentation**: http://localhost:8082/docs (Swagger UI)
   - **API Base URL**: http://localhost:8082

### Web Dashboard

The system includes a comprehensive web dashboard with the following tabs:

#### **Dashboard Tab**
- Real-time statistics cards
- Email processing metrics
- WhatsApp notification counts
- Voice call statistics
- Employee status overview

#### **Email Logs Tab**
- View all processed emails
- Filter by date, sender, status
- Detailed email information
- AI-generated summaries

#### **Employees Tab**
- Manage team members
- Add/edit/delete employees
- WhatsApp number management
- Employee status tracking

#### **Monitoring Tab**
- Action monitoring dashboard
- Success/failure rates
- Cost tracking for voice calls
- Performance analytics
- Real-time activity feed

#### **Settings Tab**
- Email configuration
- AI model settings
- WhatsApp API status
- VAPI voice calling settings

### API Endpoints

#### Email Management
- `GET /api/emails` - List all processed emails
- `GET /api/emails/{email_id}` - Get specific email details
- `POST /api/emails/{email_id}/process` - Process specific email

#### Employee Management
- `GET /api/employees` - List all employees
- `POST /api/employees` - Add new employee
- `PUT /api/employees/{employee_id}` - Update employee
- `DELETE /api/employees/{employee_id}` - Delete employee

#### Notifications
- `GET /api/notifications` - List WhatsApp notifications
- `POST /api/notifications/retry` - Retry failed notifications

#### Voice Calls (VAPI)
- `POST /api/calls/retry` - Retry failed voice calls
- `POST /api/calls/retry/start` - Start background call retry monitoring

#### Email Replies
- `GET /api/replies` - List email replies
- `POST /api/replies/retry` - Retry failed replies

#### Action Monitoring
- `GET /api/actions` - Get action monitoring records
- `GET /api/actions/statistics` - Get aggregated statistics
- `GET /api/actions/{action_id}` - Get specific action details

#### System Monitoring
- `POST /api/monitor/start` - Start email monitoring
- `GET /api/stats` - Get system statistics

### Background Email Monitoring

The system automatically monitors emails in the background when started. The enhanced monitoring process:

1. **Connects to IMAP server** and checks for unread emails
2. **Filters emails** by sender (if configured)
3. **Processes emails** and stores in database
4. **AI Analysis** generates summaries and auto-reply text
5. **Sends WhatsApp notifications** to all active team members
6. **Makes voice calls** via VAPI to all active employees (if enabled)
7. **Sends auto-reply** to original sender
8. **Logs all actions** in the monitoring system
9. **Retries failed operations** automatically with 10-minute intervals
10. **Repeats** every 5 minutes (configurable)

### VAPI Voice Calling Workflow

When an email is processed and VAPI is enabled:

1. **Voice calls initiated** simultaneously to all active employees
2. **Personalized messages** include employee name and email details
3. **Call status tracked** in real-time (pending, ringing, answered, completed, failed)
4. **Failed calls scheduled** for automatic retry in 10 minutes
5. **Maximum 3 retries** per call to prevent spam
6. **Cost tracking** for budget monitoring
7. **Background service** monitors and processes retries

### Action Monitoring System

Every system activity is comprehensively tracked:

1. **Email processing** events with timing and success status
2. **WhatsApp notifications** with recipient details and delivery status
3. **Voice calls** with duration, cost, and call outcomes
4. **Email replies** with success/failure tracking
5. **Employee management** actions (add, update, delete)
6. **System events** and API requests
7. **Error logging** with detailed error messages
8. **Performance metrics** including duration and success rates

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run specific test files
pytest tests/test_integration.py
pytest tests/test_meta_api.py

# Run with coverage
pytest --cov=app tests/
```

## 🎯 Key Features

### Enhanced Email Processing
Configure `ALLOWED_SENDER_EMAIL` to only process emails from specific senders:
```env
ALLOWED_SENDER_EMAIL=<EMAIL>
```

### Meta WhatsApp Cloud API Integration
- ✅ Uses official Meta WhatsApp Cloud API (no Twilio dependency)
- ✅ Supports international phone number formats
- ✅ Automatic retry logic for failed messages
- ✅ Comprehensive error handling and status tracking
- ✅ Real-time delivery confirmation

### VAPI Voice Calling System
- ✅ Automated voice calls to employees when emails are received
- ✅ Personalized voice messages with employee names and email details
- ✅ Smart retry logic with 10-minute intervals (max 3 attempts)
- ✅ Real-time call status tracking and cost monitoring
- ✅ Background service for automatic retry processing
- ✅ Integration with employee management system

### Advanced AI Summarization
- ✅ OpenAI GPT-4 integration for intelligent email analysis
- ✅ Generates concise summaries, WhatsApp notifications, and auto-replies
- ✅ Fallback to local LLM support for privacy-conscious deployments
- ✅ Structured data extraction and content analysis
- ✅ Context-aware response generation

### Comprehensive Action Monitoring
- ✅ Complete audit trail of all system activities
- ✅ Real-time performance metrics and success rates
- ✅ Cost tracking for voice calls and API usage
- ✅ Error pattern analysis and troubleshooting insights
- ✅ Interactive dashboard with filtering and analytics
- ✅ Automated alerting for system issues

### Employee Management System
- ✅ Full CRUD operations for team member management
- ✅ WhatsApp number validation and formatting
- ✅ Employee status tracking (active/inactive)
- ✅ Integration with notification and calling systems
- ✅ Bulk operations and data import/export

### Database & Storage
- ✅ PostgreSQL database with comprehensive schema
- ✅ Complete audit trail of all emails, notifications, calls, and replies
- ✅ Status tracking for all operations with retry counters
- ✅ Error logging with detailed diagnostic information
- ✅ SQLAlchemy ORM with migration support
- ✅ Optimized queries and indexing for performance

## 🛠️ Development

### Project Architecture
- `app/models.py` - Database models (EmailLog, VoiceCall, ActionMonitor, Employee)
- `app/main.py` - FastAPI application and API routes
- `app/worker.py` - Background worker and main entry point
- `app/services/` - Core business logic services
- `app/utils/` - Utility functions and helpers
- `frontend/` - Web dashboard interface
- `tests/` - Test suite

### Adding New Features
1. **Backend Services**: Add new service modules in `app/services/`
2. **Database Models**: Update database models in `app/models.py`
3. **API Endpoints**: Add API endpoints in `app/worker.py`
4. **Frontend Components**: Update dashboard in `frontend/`
5. **Action Monitoring**: Integrate with action monitoring system
6. **Tests**: Write comprehensive tests in `tests/`

### Development Workflow
1. **Setup Development Environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   cp .env.example .env
   ```

2. **Database Setup**:
   ```bash
   # The application will automatically create tables on startup
   python -m app.worker
   ```

3. **Frontend Development**:
   ```bash
   # Serve frontend files (development)
   python -m http.server 8080 --directory frontend
   ```

4. **Testing**:
   ```bash
   pytest tests/ -v
   pytest --cov=app tests/
   ```

### Code Style and Standards
- **Python**: Follow PEP 8 style guidelines
- **JavaScript**: Use ES6+ features and modern syntax
- **HTML/CSS**: Semantic HTML with responsive design
- **API Design**: RESTful endpoints with proper HTTP status codes
- **Error Handling**: Comprehensive error logging and user feedback

## Production Deployment

### Using Docker (Recommended)
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "-m", "app.worker"]
```

### Using Supervisor
```ini
[program:email_monitor]
command=python -m app.worker
directory=/path/to/email_monitor_fastapi
user=www-data
autostart=true
autorestart=true
```

## 🔧 Troubleshooting

### Common Issues

1. **IMAP Connection Failed**
   - Check email credentials and app passwords
   - Verify IMAP is enabled for your email account
   - Check firewall and network connectivity
   - Ensure SSL/TLS settings are correct

2. **WhatsApp API Errors**
   - Verify Meta access token is valid and not expired
   - Check phone number ID is correct
   - Ensure phone numbers are in international format (+**********)
   - Verify WhatsApp Business account is approved
   - Check API rate limits and quotas

3. **VAPI Voice Call Issues**
   - Verify VAPI API key is valid
   - Check assistant ID is correct
   - Ensure phone numbers include country codes
   - Monitor VAPI dashboard for call status
   - Check account balance for sufficient credits

4. **OpenAI API Issues**
   - Verify API key is valid and has credits
   - Check rate limits and quotas
   - Monitor API usage in OpenAI dashboard
   - Ensure model availability (GPT-4 access)

5. **Database Connection Issues**
   - Verify PostgreSQL is running
   - Check database credentials in .env file
   - Ensure database exists and is accessible
   - Check network connectivity to database server

6. **Frontend Dashboard Issues**
   - Clear browser cache and cookies
   - Check browser console for JavaScript errors
   - Verify API endpoints are accessible
   - Ensure proper CORS configuration

### Monitoring and Debugging

#### Action Monitoring Dashboard
Use the monitoring tab to identify issues:
- Check success/failure rates
- Review error messages in action logs
- Monitor performance metrics
- Track cost trends

#### Log Analysis
```bash
# Check application logs
tail -f logs/email_monitor.log

# Check specific service logs
grep "VAPI" logs/email_monitor.log
grep "WhatsApp" logs/email_monitor.log
grep "ERROR" logs/email_monitor.log
```

#### Database Queries for Troubleshooting
```sql
-- Check recent failed actions
SELECT * FROM action_monitors
WHERE success = 'false'
ORDER BY initiated_at DESC LIMIT 10;

-- Check voice call success rate
SELECT
    COUNT(*) as total_calls,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_calls,
    ROUND(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM voice_calls
WHERE initiated_at >= NOW() - INTERVAL 24 HOUR;

-- Check employee status
SELECT name, phone_number, is_active, created_at
FROM employees
ORDER BY created_at DESC;
```

### Performance Optimization

#### Database Optimization
- Add indexes on frequently queried columns
- Archive old action monitoring data
- Use connection pooling for high traffic
- Monitor query performance

#### API Performance
- Implement caching for frequently accessed data
- Use async operations for I/O bound tasks
- Monitor API response times
- Set appropriate timeout values

#### Cost Optimization
- Monitor VAPI call costs in dashboard
- Set budget alerts for voice calling
- Optimize retry logic to reduce unnecessary calls
- Use mock mode for development/testing

## 📊 System Overview

### Complete Email Processing Pipeline

```
📧 Email Received
    ↓
🤖 AI Analysis & Summarization
    ↓
📱 WhatsApp Notifications → All Active Employees
    ↓
📞 Voice Calls (VAPI) → All Active Employees
    ↓
📧 Auto-Reply → Original Sender
    ↓
📊 Action Monitoring → Dashboard & Analytics
```

### Technology Stack

- **Backend**: FastAPI, SQLAlchemy, PostgreSQL
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **AI**: OpenAI GPT-4, Local LLM support
- **Notifications**: Meta WhatsApp Cloud API
- **Voice Calls**: VAPI (Voice AI Platform)
- **Monitoring**: Custom action tracking system
- **Deployment**: Docker, Supervisor, Cloud-ready

### Key Integrations

| Service | Purpose | Status |
|---------|---------|--------|
| **OpenAI GPT-4** | Email analysis & summarization | ✅ Active |
| **Meta WhatsApp** | Team notifications | ✅ Active |
| **VAPI** | Voice calling system | ✅ Active |
| **PostgreSQL** | Data persistence | ✅ Active |
| **IMAP/SMTP** | Email processing | ✅ Active |

## 🚀 Quick Start Guide

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd email_monitor_fastapi
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Environment Configuration**:
   The `.env` file is already configured with your settings. To customize:
   ```bash
   # Edit .env file with your specific API keys and settings
   # Current configuration includes:
   # - PostgreSQL database (Email_agent)
   # - Gmail IMAP/SMTP settings
   # - OpenAI GPT-4 integration
   # - WhatsApp Business API
   # - VAPI voice calling (needs API key configuration)
   ```

3. **Database Setup**:
   ```bash
   # Ensure PostgreSQL is running with the configured database
   # Database: Email_agent
   # User: postgres
   # Password: admin
   # The application will automatically create tables on startup
   ```

4. **Start the System**:
   ```bash
   python -m app.worker
   ```

5. **Access Dashboard**:
   - Open http://localhost:8000 in your browser
   - Monitor real-time statistics and activities

### 🔧 **Current Configuration Status**

✅ **Fully Configured:**
- PostgreSQL Database (Email_agent)
- Gmail IMAP/SMTP (<EMAIL>)
- OpenAI GPT-4 Integration
- WhatsApp Business API (Meta Cloud API)

⚠️ **Needs Configuration:**
- VAPI Voice Calling (API key required)
  - Update `VAPI_API_KEY` in `.env`
  - Update `VAPI_ASSISTANT_ID` in `.env`
  - Optionally set `VAPI_PHONE_NUMBER_ID`

## 📚 Documentation

- **[VAPI Integration Guide](VAPI_INTEGRATION.md)** - Complete VAPI setup and usage
- **[Action Monitoring Guide](ACTION_MONITORING.md)** - Monitoring system documentation
- **[API Documentation](http://localhost:8000/docs)** - Interactive Swagger UI (when running)

## 🤝 Contributing

We welcome contributions! Please follow these guidelines:

### Development Setup
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests pass: `pytest tests/`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

### Code Standards
- Follow PEP 8 for Python code
- Add docstrings to all functions and classes
- Write tests for new features
- Update documentation as needed
- Ensure action monitoring integration for new features

### Reporting Issues
- Use the GitHub issue tracker
- Include detailed reproduction steps
- Provide system information and logs
- Check existing issues before creating new ones

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenAI** for GPT-4 API
- **Meta** for WhatsApp Cloud API
- **VAPI** for voice calling platform
- **FastAPI** for the excellent web framework
- **SQLAlchemy** for database ORM

## 📞 Support

For support and questions:
- 📧 Email: [<EMAIL>]
- 💬 GitHub Issues: [Create an issue](../../issues)
- 📖 Documentation: Check the docs folder
- 🔍 Monitoring: Use the built-in action monitoring dashboard

---

**Email Monitor Agent** - Intelligent email processing with AI-powered analysis, multi-channel notifications, and comprehensive monitoring. Built for reliability, scalability, and ease of use.
