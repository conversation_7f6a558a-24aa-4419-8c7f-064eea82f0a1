# Twilio SMS and Voice Calling Integration

This document explains how to set up and use the Twilio API integration for sending SMS notifications and making voice calls to employees when email notifications are received.

## 🎯 **Features**

### **SMS Notifications**
- ✅ **Automatic SMS** to employees when emails are received
- ✅ **Individual messages** to each active employee
- ✅ **Personalized messages** with employee names and email details
- ✅ **Delivery tracking** with message IDs and status
- ✅ **Error handling** with detailed logging

### **Voice Calls**
- ✅ **Automatic voice calls** to employees when WhatsApp notifications are sent
- ✅ **Individual calls** to each active employee
- ✅ **Personalized messages** with employee names and email details
- ✅ **Automatic retry logic** for failed or unanswered calls
- ✅ **TwiML-based** voice message generation

### **Smart Retry System**
- ✅ **10-minute delays** for rescheduling failed calls
- ✅ **Maximum 3 retries** per call to avoid spam
- ✅ **Background monitoring** for automatic retries
- ✅ **Permanent failure** marking after max retries

## 🚀 **Setup Instructions**

### **1. Twilio Account Setup**

1. **Create Account**: Sign up at [twilio.com](https://twilio.com)
2. **Get API Credentials**: 
   - Go to your Console Dashboard and note your Account SID and Auth Token
3. **Get Phone Number**:
   - Purchase a phone number for SMS and voice calls
   - Note the phone number in international format (e.g., +**********)

### **2. Environment Configuration**

Add these variables to your `.env` file:

```env
# SMS notifications with Twilio
SMS_ENABLED=True
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_SMS_FROM_NUMBER=+**********  # Your Twilio phone number

# Voice calling with Twilio
VOICE_CALLING_ENABLED=True
VOICE_PROVIDER=twilio
TWILIO_FROM_NUMBER=+**********  # Your Twilio phone number
```

### **3. Database Migration**
The system will automatically create the necessary tables when you start the application. The tables include:
- `sms_notifications` - SMS tracking information
- `voice_call_logs` - Call tracking information
- Status and timing data
- Retry scheduling
- Error logging

## 🔧 **How It Works**

### **SMS Flow**
1. **Email Received** → System processes new email
2. **Employee Lookup** → Gets active employees from management dashboard
3. **SMS Generation** → Creates personalized SMS for each employee
4. **Twilio API Call** → Sends SMS via Twilio REST API
5. **Status Tracking** → Records delivery status and message IDs
6. **Error Handling** → Logs any failures for debugging

### **Voice Call Flow**
1. **WhatsApp Sent** → After WhatsApp notifications are sent
2. **Call Scheduling** → Schedules voice calls to all employees
3. **TwiML Generation** → Creates voice message script
4. **Twilio Voice API** → Initiates calls via Twilio
5. **Status Monitoring** → Tracks call status and duration
6. **Retry Logic** → Automatically retries failed calls after 10 minutes

## 📱 **SMS Message Format**

```
Hi [FirstName], New email from [SenderName]
Subject: [EmailSubject]
Time: [HH:MM]
- EmailAgent
```

## 📞 **Voice Message Format**

```
Hello [EmployeeName], this is an automated notification from the Email Monitor Agent. 
You have received an important email from [SenderName]. 
The subject is: [EmailSubject]. 
Please check your email as soon as possible. Thank you.
```

## 🧪 **Testing**

### **Test SMS Integration**
```bash
python tests/test_twilio_sms_direct.py
```

### **Test Voice Integration**
```bash
python tests/test_twilio_integration.py
```

### **Test All Employee Numbers**
```bash
python test_all_numbers.py
```

## 💰 **Pricing Considerations**

- **SMS**: Typically $0.0075 per SMS in the US, varies by country
- **Voice**: Typically $0.013 per minute in the US, varies by country
- **Phone Number**: Monthly rental fee (varies by country)
- Check current pricing at [twilio.com/pricing](https://twilio.com/pricing)

## 🔍 **Monitoring & Debugging**

### **SMS Status Codes**
- `queued` - Message is queued for delivery
- `sent` - Message has been sent
- `delivered` - Message was delivered
- `failed` - Message failed to send
- `undelivered` - Message could not be delivered

### **Voice Call Status**
- `queued` - Call is queued
- `ringing` - Phone is ringing
- `in-progress` - Call is active
- `completed` - Call completed successfully
- `busy` - Recipient was busy
- `no-answer` - No one answered
- `failed` - Call failed

### **Logs Location**
- Application logs contain detailed SMS and call information
- Check console output for real-time status
- Database tables store historical data

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **SMS Not Sending**
   - Check Account SID and Auth Token
   - Verify phone number format (+country code)
   - Check Twilio account balance
   - Verify phone number is SMS-enabled

2. **Voice Calls Failing**
   - Check Account SID and Auth Token
   - Verify phone number is voice-enabled
   - Check TwiML syntax
   - Verify recipient numbers are valid

3. **Authentication Errors**
   - Double-check Account SID and Auth Token
   - Ensure credentials are not expired
   - Check for typos in environment variables

### **Debug Commands**
```bash
# Test Twilio credentials
python tests/test_twilio_sms_direct.py

# Check account balance
python -c "from twilio.rest import Client; import os; client = Client(os.getenv('TWILIO_ACCOUNT_SID'), os.getenv('TWILIO_AUTH_TOKEN')); print(client.balance.fetch().balance)"

# Test phone number capabilities
python -c "from twilio.rest import Client; import os; client = Client(os.getenv('TWILIO_ACCOUNT_SID'), os.getenv('TWILIO_AUTH_TOKEN')); print(client.incoming_phone_numbers.list())"
```

## 🔐 **Security Best Practices**

1. **Environment Variables**: Store credentials in `.env` file, never in code
2. **Access Control**: Limit Twilio account permissions if possible
3. **Rate Limiting**: Monitor usage to prevent abuse
4. **Webhook Security**: Use webhook signatures if implementing webhooks
5. **Regular Rotation**: Periodically rotate Auth Tokens

## 📚 **Additional Resources**

- [Twilio SMS API Documentation](https://www.twilio.com/docs/sms)
- [Twilio Voice API Documentation](https://www.twilio.com/docs/voice)
- [TwiML Reference](https://www.twilio.com/docs/voice/twiml)
- [Twilio Python Helper Library](https://www.twilio.com/docs/libraries/python)
