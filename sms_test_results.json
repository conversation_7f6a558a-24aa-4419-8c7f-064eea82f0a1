{"test_time": "12:22:06", "total_employees": 4, "successful_sends": 1, "failed_sends": 3, "results": [{"employee_name": "<PERSON>", "phone": "+************", "employee_id": 1, "success": false, "message_id": null, "error": "Twi<PERSON> SMS error: \n\u001b[31m\u001b[49mHTTP Error\u001b[0m \u001b[37m\u001b[49mYour request was:\u001b[0m\n\n\u001b[36m\u001b[49mPOST /Accounts/ACb96b37393fc14dfcd2ed1307651c120e/Messages.json\u001b[0m\n\n\u001b[37m\u001b[49mT<PERSON><PERSON> returned the following information:\u001b[0m\n\n\u001b[34m\u001b[49mUnable to create record: Account ACb96b37393fc14dfcd2ed1307651c120e exceeded the 9 daily messages limit\u001b[0m\n\n\u001b[37m\u001b[49mMore information may be available here:\u001b[0m\n\n\u001b[34m\u001b[49mhttps://www.twilio.com/docs/errors/63038\u001b[0m\n\n", "test_time": "12:22:06", "message_sent": "🧪 SMS Test #1 for <PERSON> at 12:22:06. Please reply 'GOT IT' if you receive this message!"}, {"employee_name": "<PERSON>", "phone": "+************", "employee_id": 2, "success": true, "message_id": "SMf614d8b4419ff52c6ebe17cdc0002b90", "error": null, "test_time": "12:22:06", "message_sent": "🧪 SMS Test #2 for <PERSON> at 12:22:06. Please reply 'GOT IT' if you receive this message!"}, {"employee_name": "karthik", "phone": "+************", "employee_id": 3, "success": false, "message_id": null, "error": "Twi<PERSON> SMS error: \n\u001b[31m\u001b[49mHTTP Error\u001b[0m \u001b[37m\u001b[49mYour request was:\u001b[0m\n\n\u001b[36m\u001b[49mPOST /Accounts/ACb96b37393fc14dfcd2ed1307651c120e/Messages.json\u001b[0m\n\n\u001b[37m\u001b[49mT<PERSON><PERSON> returned the following information:\u001b[0m\n\n\u001b[34m\u001b[49mUnable to create record: Account ACb96b37393fc14dfcd2ed1307651c120e exceeded the 9 daily messages limit\u001b[0m\n\n\u001b[37m\u001b[49mMore information may be available here:\u001b[0m\n\n\u001b[34m\u001b[49mhttps://www.twilio.com/docs/errors/63038\u001b[0m\n\n", "test_time": "12:22:06", "message_sent": "🧪 SMS Test #3 for karthik at 12:22:06. Please reply 'GOT IT' if you receive this message!"}, {"employee_name": "nirmal", "phone": "+************", "employee_id": 6, "success": false, "message_id": null, "error": "Twi<PERSON> SMS error: \n\u001b[31m\u001b[49mHTTP Error\u001b[0m \u001b[37m\u001b[49mYour request was:\u001b[0m\n\n\u001b[36m\u001b[49mPOST /Accounts/ACb96b37393fc14dfcd2ed1307651c120e/Messages.json\u001b[0m\n\n\u001b[37m\u001b[49mT<PERSON><PERSON> returned the following information:\u001b[0m\n\n\u001b[34m\u001b[49mUnable to create record: Account ACb96b37393fc14dfcd2ed1307651c120e exceeded the 9 daily messages limit\u001b[0m\n\n\u001b[37m\u001b[49mMore information may be available here:\u001b[0m\n\n\u001b[34m\u001b[49mhttps://www.twilio.com/docs/errors/63038\u001b[0m\n\n", "test_time": "12:22:06", "message_sent": "🧪 SMS Test #4 for nirmal at 12:22:06. Please reply 'GOT IT' if you receive this message!"}]}